"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BrowsingNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const maxSites = (config === null || config === void 0 ? void 0 : config.maxSites) || 5;\n    const timeout = (config === null || config === void 0 ? void 0 : config.timeout) || 30;\n    var _config_enableScreenshots;\n    const enableScreenshots = (_config_enableScreenshots = config === null || config === void 0 ? void 0 : config.enableScreenshots) !== null && _config_enableScreenshots !== void 0 ? _config_enableScreenshots : true;\n    var _config_enableFormFilling;\n    const enableFormFilling = (_config_enableFormFilling = config === null || config === void 0 ? void 0 : config.enableFormFilling) !== null && _config_enableFormFilling !== void 0 ? _config_enableFormFilling : true;\n    const searchEngines = (config === null || config === void 0 ? void 0 : config.searchEngines) || [\n        'google'\n    ];\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (enableScreenshots) capabilities.push('📸 Screenshots');\n        if (enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#10b981\",\n        hasInput: false,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'plan',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Intelligent Browsing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400\",\n                                    children: \"● Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max sites: \",\n                                maxSites,\n                                \" | Timeout: \",\n                                timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Engines: \",\n                                searchEngines.join(', ')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: getCapabilities().join(' • ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max depth: \",\n                                config.maxDepth,\n                                \" levels\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83C\\uDF10 Autonomous Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Requires: Planner + Memory inputs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ })

});