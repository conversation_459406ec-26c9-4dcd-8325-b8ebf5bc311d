"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/ProviderNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction ProviderNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#ff6b35';\n    const providerName = providerId ? providerNames[providerId] : 'AI Provider';\n    // Get connected role nodes - using reactive hooks for automatic re-renders\n    const connectedRoles = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            console.log('Role Agent Node Data:', sourceNode.data); // Debug log\n            console.log('Role Config:', roleConfig); // Debug log\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || sourceNode.data.label || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#ff6b35',\n        hasInput: true,\n        hasOutput: true,\n        hasRoleInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            providerId === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"300+ Models\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 15\n                    }, this),\n                    connectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: \"Roles:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: connectedRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                        children: role.name\n                                    }, role.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 66,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"AI Provider Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 119,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(ProviderNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = ProviderNode;\nvar _c;\n$RefreshReg$(_c, \"ProviderNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\n"));

/***/ })

});