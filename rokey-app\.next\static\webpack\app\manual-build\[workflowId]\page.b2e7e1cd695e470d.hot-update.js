"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, hasToolsInput = false, hasBrowsingInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', toolsInputLabel = 'Tools', browsingInputLabel = 'Browsing', inputHandles = [], className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-gray-300 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '45%'\n                        },\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '30%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-purple-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '25%'\n                        },\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasToolsInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"tools\",\n                        className: \"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '70%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-green-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '65%'\n                        },\n                        children: toolsInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasBrowsingInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"browsing\",\n                        className: \"w-6 h-6 border-2 border-emerald-500 bg-emerald-700 hover:border-emerald-400 hover:bg-emerald-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '85%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-emerald-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -60,\n                            top: '80%'\n                        },\n                        children: browsingInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            inputHandles.length > 0 && inputHandles.map((handle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                            type: \"target\",\n                            position: handle.position === 'left' ? _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left : _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Top,\n                            id: handle.id,\n                            className: \"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors\",\n                            style: {\n                                left: handle.position === 'left' ? -12 : undefined,\n                                top: handle.position === 'left' ? \"\".concat(30 + index * 35, \"%\") : -12\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-xs text-blue-200 font-medium pointer-events-none\",\n                            style: {\n                                left: handle.position === 'left' ? -60 : undefined,\n                                top: handle.position === 'left' ? \"\".concat(25 + index * 35, \"%\") : -25,\n                                right: handle.position === 'top' ? undefined : 'auto'\n                            },\n                            children: handle.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, handle.id, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"source\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                        className: \"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors\",\n                        style: {\n                            right: -12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-orange-200 font-medium pointer-events-none\",\n                        style: {\n                            right: -60,\n                            top: '45%'\n                        },\n                        children: outputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});