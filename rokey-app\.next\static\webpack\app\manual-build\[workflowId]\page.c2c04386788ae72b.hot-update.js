"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClipboardDocumentListIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n    }));\n}\n_c = ClipboardDocumentListIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClipboardDocumentListIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ClipboardDocumentListIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'planner',\n                label: 'Planner',\n                description: 'AI model that creates browsing strategies and todo lists',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Planner',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000\n                        },\n                        maxSubtasks: 10\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Web Browsing, Google Drive, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 353,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});