"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst toolIcons = {\n    google_drive: '📁',\n    google_docs: '📄',\n    zapier: '⚡',\n    notion: '📝',\n    google_sheets: '📊',\n    calendar: '📅',\n    gmail: '📧',\n    youtube: '📺',\n    supabase: '🗄️'\n};\nconst toolNames = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    zapier: 'Zapier',\n    notion: 'Notion',\n    google_sheets: 'Google Sheets',\n    calendar: 'Calendar',\n    gmail: 'Gmail',\n    youtube: 'YouTube',\n    supabase: 'Supabase'\n};\nfunction ToolNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? toolIcons[toolType] : '🔧';\n    const toolName = toolType ? toolNames[toolType] : 'External Tool';\n    const isWebBrowsing = toolType === 'web_browsing';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const getStatusColor = ()=>{\n        if (isWebBrowsing) return 'text-green-400'; // Web browsing is always available\n        switch(connectionStatus){\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        if (isWebBrowsing) return 'Ready';\n        switch(connectionStatus){\n            case 'connected':\n                return 'Connected';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#06b6d4\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: toolIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(getStatusColor()),\n                                children: \"●\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this),\n                    isWebBrowsing && (config === null || config === void 0 ? void 0 : config.searchEngine) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Engine: \",\n                            config.searchEngine\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Timeout: \",\n                            config.timeout,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                        children: \"✓ Tool configured\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 68,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"External Tool Integration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Connect to external services like Google Drive, databases, APIs, or browser automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 102,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ })

});