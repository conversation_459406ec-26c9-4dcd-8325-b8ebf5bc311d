"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/VisionNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisionNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction VisionNode(param) {\n    let { data, id } = param;\n    _s();\n    const { getEdges, getNodes } = useReactFlow();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#8b5cf6'; // Purple default for vision\n    const providerName = providerId ? providerNames[providerId] : 'Vision AI';\n    // Get connected role nodes\n    const connectedRoles = getEdges().filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = getNodes().find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            console.log('Vision Node - Role Agent Data:', sourceNode.data); // Debug log\n            console.log('Vision Node - Role Config:', roleConfig); // Debug log\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || sourceNode.data.label || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: typeof color === 'string' ? color : '#8b5cf6',\n        hasInput: true,\n        hasOutput: true,\n        hasRoleInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"Vision\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this),\n                    connectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: \"Roles:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: connectedRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                        children: role.name\n                                    }, role.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 65,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Vision AI Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to multimodal AI models for image analysis and vision tasks.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 116,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(VisionNode, \"RZ7Xaupw8bsRjC9QeKwHIIgcTfo=\", true);\n_c = VisionNode;\nvar _c;\n$RefreshReg$(_c, \"VisionNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\n"));

/***/ })

});