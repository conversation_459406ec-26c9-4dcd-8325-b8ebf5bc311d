"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/manual-build/nodes/CentralRouterNode.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CentralRouterNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CentralRouterNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useNodes)();\n    const config = data.config;\n    // Get connected AI providers\n    const connectedProviders = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'providers').map((edge)=>{\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'provider') {\n            const providerConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || 'Unknown Provider',\n                model: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || 'Unknown Model'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    // Get connected vision nodes\n    const connectedVision = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'vision').map((edge)=>{\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'vision') {\n            const visionConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || 'Unknown Vision Provider',\n                model: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || 'Unknown Model'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    // Check if classifier is connected\n    const hasClassifier = edges.some((edge)=>edge.target === id && edge.targetHandle === 'classifier' && nodes.find((node)=>node.id === edge.source && node.type === 'classifier'));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"providers\",\n                className: \"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '20%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-blue-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '15%'\n                },\n                children: \"AI Providers\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"vision\",\n                className: \"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '50%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-purple-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '45%'\n                },\n                children: \"Vision Models\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                id: \"classifier\",\n                className: \"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                style: {\n                    left: -12,\n                    top: '80%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-green-200 font-medium pointer-events-none\",\n                style: {\n                    left: -80,\n                    top: '75%'\n                },\n                children: \"Classifier\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Top,\n                id: \"input\",\n                className: \"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    top: -12\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-gray-300 font-medium pointer-events-none\",\n                style: {\n                    top: -30,\n                    left: '45%'\n                },\n                children: \"Input\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                id: \"output\",\n                className: \"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors\",\n                style: {\n                    right: -12\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-orange-200 font-medium pointer-events-none\",\n                style: {\n                    right: -60,\n                    top: '45%'\n                },\n                children: \"Output\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[280px] rounded-lg border-2 transition-all duration-200 \".concat(data.hasError ? 'border-red-500 bg-red-900/20' : data.isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: data.hasError ? '#ef4444' : data.isConfigured ? '#ff6b35' : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: data.hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : 'linear-gradient(135deg, #ff6b3520, #ff6b3510)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: data.hasError ? '#ef444420' : '#ff6b3520',\n                                    color: data.hasError ? '#ef4444' : '#ff6b35'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: \"Central Router\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Smart routing hub for AI providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                        title: \"Classifier Connected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    connectedProviders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-500 rounded-full\",\n                                        title: \"\".concat(connectedProviders.length, \" AI Providers\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    connectedVision.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full\",\n                                        title: \"\".concat(connectedVision.length, \" Vision Models\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 space-y-3\",\n                        children: [\n                            connectedProviders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"AI Providers (\",\n                                            connectedProviders.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: connectedProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30\",\n                                                children: provider.name\n                                            }, provider.id, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            connectedVision.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"Vision Models (\",\n                                            connectedVision.length,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1\",\n                                        children: connectedVision.map((vision)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                                children: vision.name\n                                            }, vision.id, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this),\n                            (config === null || config === void 0 ? void 0 : config.routingStrategy) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                                children: [\n                                    \"Strategy: \",\n                                    config.routingStrategy.replace('_', ' ').toUpperCase()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            !hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                                children: \"⚠️ Connect classifier for smart routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            connectedProviders.length === 0 && connectedVision.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded\",\n                                children: \"❌ No AI providers connected\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            (connectedProviders.length > 0 || connectedVision.length > 0) && hasClassifier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                                children: \"✅ Ready for smart routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\CentralRouterNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(CentralRouterNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_1__.useNodes\n    ];\n});\n_c = CentralRouterNode;\nvar _c;\n$RefreshReg$(_c, \"CentralRouterNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9DZW50cmFsUm91dGVyTm9kZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwRDtBQUNzQjtBQUlqRSxTQUFTSyxrQkFBa0IsS0FBNkM7UUFBN0MsRUFBRUMsSUFBSSxFQUFFQyxFQUFFLEVBQW1DLEdBQTdDOztJQUN4QyxNQUFNQyxRQUFRUCx1REFBUUE7SUFDdEIsTUFBTVEsUUFBUVAsdURBQVFBO0lBQ3RCLE1BQU1RLFNBQVNKLEtBQUtJLE1BQU07SUFFMUIsNkJBQTZCO0lBQzdCLE1BQU1DLHFCQUFxQkgsTUFDeEJJLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsTUFBTSxLQUFLUCxNQUFNTSxLQUFLRSxZQUFZLEtBQUssYUFDM0RDLEdBQUcsQ0FBQ0gsQ0FBQUE7UUFDSCxNQUFNSSxhQUFhUixNQUFNUyxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtaLEVBQUUsS0FBS00sS0FBS08sTUFBTTtRQUM3RCxJQUFJSCxjQUFjQSxXQUFXSSxJQUFJLEtBQUssWUFBWTtZQUNoRCxNQUFNQyxpQkFBaUJMLFdBQVdYLElBQUksQ0FBQ0ksTUFBTTtZQUM3QyxPQUFPO2dCQUNMSCxJQUFJTSxLQUFLTyxNQUFNO2dCQUNmRyxNQUFNRCxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCRSxVQUFVLEtBQUk7Z0JBQ3BDQyxPQUFPSCxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCSSxPQUFPLEtBQUk7WUFDcEM7UUFDRjtRQUNBLE9BQU87SUFDVCxHQUNDZCxNQUFNLENBQUNlO0lBRVYsNkJBQTZCO0lBQzdCLE1BQU1DLGtCQUFrQnBCLE1BQ3JCSSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLE1BQU0sS0FBS1AsTUFBTU0sS0FBS0UsWUFBWSxLQUFLLFVBQzNEQyxHQUFHLENBQUNILENBQUFBO1FBQ0gsTUFBTUksYUFBYVIsTUFBTVMsSUFBSSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLWixFQUFFLEtBQUtNLEtBQUtPLE1BQU07UUFDN0QsSUFBSUgsY0FBY0EsV0FBV0ksSUFBSSxLQUFLLFVBQVU7WUFDOUMsTUFBTVEsZUFBZVosV0FBV1gsSUFBSSxDQUFDSSxNQUFNO1lBQzNDLE9BQU87Z0JBQ0xILElBQUlNLEtBQUtPLE1BQU07Z0JBQ2ZHLE1BQU1NLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY0wsVUFBVSxLQUFJO2dCQUNsQ0MsT0FBT0ksQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjSCxPQUFPLEtBQUk7WUFDbEM7UUFDRjtRQUNBLE9BQU87SUFDVCxHQUNDZCxNQUFNLENBQUNlO0lBRVYsbUNBQW1DO0lBQ25DLE1BQU1HLGdCQUFnQnRCLE1BQU11QixJQUFJLENBQUNsQixDQUFBQSxPQUMvQkEsS0FBS0MsTUFBTSxLQUFLUCxNQUNoQk0sS0FBS0UsWUFBWSxLQUFLLGdCQUN0Qk4sTUFBTVMsSUFBSSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLWixFQUFFLEtBQUtNLEtBQUtPLE1BQU0sSUFBSUQsS0FBS0UsSUFBSSxLQUFLO0lBRzlELHFCQUNFLDhEQUFDVztRQUFJQyxXQUFVOzswQkFJYiw4REFBQzlCLGlEQUFNQTtnQkFDTGtCLE1BQUs7Z0JBQ0xhLFVBQVU5QixtREFBUUEsQ0FBQytCLElBQUk7Z0JBQ3ZCNUIsSUFBRztnQkFDSDBCLFdBQVU7Z0JBQ1ZHLE9BQU87b0JBQUVDLE1BQU0sQ0FBQztvQkFBSUMsS0FBSztnQkFBTTs7Ozs7OzBCQUVqQyw4REFBQ047Z0JBQUlDLFdBQVU7Z0JBQWlFRyxPQUFPO29CQUFFQyxNQUFNLENBQUM7b0JBQUlDLEtBQUs7Z0JBQU07MEJBQUc7Ozs7OzswQkFLbEgsOERBQUNuQyxpREFBTUE7Z0JBQ0xrQixNQUFLO2dCQUNMYSxVQUFVOUIsbURBQVFBLENBQUMrQixJQUFJO2dCQUN2QjVCLElBQUc7Z0JBQ0gwQixXQUFVO2dCQUNWRyxPQUFPO29CQUFFQyxNQUFNLENBQUM7b0JBQUlDLEtBQUs7Z0JBQU07Ozs7OzswQkFFakMsOERBQUNOO2dCQUFJQyxXQUFVO2dCQUFtRUcsT0FBTztvQkFBRUMsTUFBTSxDQUFDO29CQUFJQyxLQUFLO2dCQUFNOzBCQUFHOzs7Ozs7MEJBS3BILDhEQUFDbkMsaURBQU1BO2dCQUNMa0IsTUFBSztnQkFDTGEsVUFBVTlCLG1EQUFRQSxDQUFDK0IsSUFBSTtnQkFDdkI1QixJQUFHO2dCQUNIMEIsV0FBVTtnQkFDVkcsT0FBTztvQkFBRUMsTUFBTSxDQUFDO29CQUFJQyxLQUFLO2dCQUFNOzs7Ozs7MEJBRWpDLDhEQUFDTjtnQkFBSUMsV0FBVTtnQkFBa0VHLE9BQU87b0JBQUVDLE1BQU0sQ0FBQztvQkFBSUMsS0FBSztnQkFBTTswQkFBRzs7Ozs7OzBCQUtuSCw4REFBQ25DLGlEQUFNQTtnQkFDTGtCLE1BQUs7Z0JBQ0xhLFVBQVU5QixtREFBUUEsQ0FBQ21DLEdBQUc7Z0JBQ3RCaEMsSUFBRztnQkFDSDBCLFdBQVU7Z0JBQ1ZHLE9BQU87b0JBQUVFLEtBQUssQ0FBQztnQkFBRzs7Ozs7OzBCQUVwQiw4REFBQ047Z0JBQUlDLFdBQVU7Z0JBQWlFRyxPQUFPO29CQUFFRSxLQUFLLENBQUM7b0JBQUlELE1BQU07Z0JBQU07MEJBQUc7Ozs7OzswQkFLbEgsOERBQUNsQyxpREFBTUE7Z0JBQ0xrQixNQUFLO2dCQUNMYSxVQUFVOUIsbURBQVFBLENBQUNvQyxLQUFLO2dCQUN4QmpDLElBQUc7Z0JBQ0gwQixXQUFVO2dCQUNWRyxPQUFPO29CQUFFSyxPQUFPLENBQUM7Z0JBQUc7Ozs7OzswQkFFdEIsOERBQUNUO2dCQUFJQyxXQUFVO2dCQUFtRUcsT0FBTztvQkFBRUssT0FBTyxDQUFDO29CQUFJSCxLQUFLO2dCQUFNOzBCQUFHOzs7Ozs7MEJBS3JILDhEQUFDTjtnQkFDQ0MsV0FBVyxpRUFNVixPQUxDM0IsS0FBS29DLFFBQVEsR0FDVCxpQ0FDQXBDLEtBQUtxQyxZQUFZLEdBQ2pCLG1DQUNBLHNDQUNMO2dCQUNEUCxPQUFPO29CQUNMUSxhQUFhdEMsS0FBS29DLFFBQVEsR0FBRyxZQUFZcEMsS0FBS3FDLFlBQVksR0FBRyxZQUFZO2dCQUMzRTs7a0NBR0EsOERBQUNYO3dCQUNDQyxXQUFVO3dCQUNWRyxPQUFPOzRCQUNMUyxZQUFZdkMsS0FBS29DLFFBQVEsR0FDckIsNEVBQ0E7d0JBQ047OzBDQUVBLDhEQUFDVjtnQ0FDQ0MsV0FBVTtnQ0FDVkcsT0FBTztvQ0FDTFUsaUJBQWlCeEMsS0FBS29DLFFBQVEsR0FBRyxjQUFjO29DQUMvQ0ssT0FBT3pDLEtBQUtvQyxRQUFRLEdBQUcsWUFBWTtnQ0FDckM7MENBRUEsNEVBQUMxQyxxR0FBV0E7b0NBQUNpQyxXQUFVOzs7Ozs7Ozs7OzswQ0FFekIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQWlDOzs7Ozs7a0RBR2hELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBNkI7Ozs7Ozs7Ozs7OzswQ0FNOUMsOERBQUNEO2dDQUFJQyxXQUFVOztvQ0FDWkgsK0JBQ0MsOERBQUNFO3dDQUFJQyxXQUFVO3dDQUFvQ2UsT0FBTTs7Ozs7O29DQUUxRHJDLG1CQUFtQnNDLE1BQU0sR0FBRyxtQkFDM0IsOERBQUNqQjt3Q0FBSUMsV0FBVTt3Q0FBbUNlLE9BQU8sR0FBNkIsT0FBMUJyQyxtQkFBbUJzQyxNQUFNLEVBQUM7Ozs7OztvQ0FFdkZyQixnQkFBZ0JxQixNQUFNLEdBQUcsbUJBQ3hCLDhEQUFDakI7d0NBQUlDLFdBQVU7d0NBQXFDZSxPQUFPLEdBQTBCLE9BQXZCcEIsZ0JBQWdCcUIsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTNGLDhEQUFDakI7d0JBQUlDLFdBQVU7OzRCQUVadEIsbUJBQW1Cc0MsTUFBTSxHQUFHLG1CQUMzQiw4REFBQ2pCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzRDQUF3Qjs0Q0FBZXRCLG1CQUFtQnNDLE1BQU07NENBQUM7Ozs7Ozs7a0RBQ2hGLDhEQUFDakI7d0NBQUlDLFdBQVU7a0RBQ1p0QixtQkFBbUJLLEdBQUcsQ0FBQyxDQUFDa0MseUJBQ3ZCLDhEQUFDQztnREFFQ2xCLFdBQVU7MERBRVRpQixTQUFTM0IsSUFBSTsrQ0FIVDJCLFNBQVMzQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzRCQVd6QnFCLGdCQUFnQnFCLE1BQU0sR0FBRyxtQkFDeEIsOERBQUNqQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzs0Q0FBd0I7NENBQWdCTCxnQkFBZ0JxQixNQUFNOzRDQUFDOzs7Ozs7O2tEQUM5RSw4REFBQ2pCO3dDQUFJQyxXQUFVO2tEQUNaTCxnQkFBZ0JaLEdBQUcsQ0FBQyxDQUFDb0MsdUJBQ3BCLDhEQUFDRDtnREFFQ2xCLFdBQVU7MERBRVRtQixPQUFPN0IsSUFBSTsrQ0FIUDZCLE9BQU83QyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzRCQVd2QkcsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRMkMsZUFBZSxtQkFDdEIsOERBQUNyQjtnQ0FBSUMsV0FBVTs7b0NBQXlEO29DQUMzRHZCLE9BQU8yQyxlQUFlLENBQUNDLE9BQU8sQ0FBQyxLQUFLLEtBQUtDLFdBQVc7Ozs7Ozs7NEJBS2xFLENBQUN6QiwrQkFDQSw4REFBQ0U7Z0NBQUlDLFdBQVU7MENBQTZEOzs7Ozs7NEJBSzdFdEIsbUJBQW1Cc0MsTUFBTSxLQUFLLEtBQUtyQixnQkFBZ0JxQixNQUFNLEtBQUssbUJBQzdELDhEQUFDakI7Z0NBQUlDLFdBQVU7MENBQXVEOzs7Ozs7NEJBS3RFdEIsQ0FBQUEsbUJBQW1Cc0MsTUFBTSxHQUFHLEtBQUtyQixnQkFBZ0JxQixNQUFNLEdBQUcsTUFBTW5CLCtCQUNoRSw4REFBQ0U7Z0NBQUlDLFdBQVU7MENBQTJEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRdEY7R0FyT3dCNUI7O1FBQ1JKLG1EQUFRQTtRQUNSQyxtREFBUUE7OztLQUZBRyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXG1hbnVhbC1idWlsZFxcbm9kZXNcXENlbnRyYWxSb3V0ZXJOb2RlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IENwdUNoaXBJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IE5vZGVQcm9wcywgdXNlRWRnZXMsIHVzZU5vZGVzLCBIYW5kbGUsIFBvc2l0aW9uIH0gZnJvbSAnQHh5Zmxvdy9yZWFjdCc7XG5pbXBvcnQgQmFzZU5vZGUgZnJvbSAnLi9CYXNlTm9kZSc7XG5pbXBvcnQgeyBXb3JrZmxvd05vZGUsIENlbnRyYWxSb3V0ZXJOb2RlRGF0YSB9IGZyb20gJ0AvdHlwZXMvbWFudWFsQnVpbGQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDZW50cmFsUm91dGVyTm9kZSh7IGRhdGEsIGlkIH06IE5vZGVQcm9wczxXb3JrZmxvd05vZGVbJ2RhdGEnXT4pIHtcbiAgY29uc3QgZWRnZXMgPSB1c2VFZGdlcygpO1xuICBjb25zdCBub2RlcyA9IHVzZU5vZGVzKCk7XG4gIGNvbnN0IGNvbmZpZyA9IGRhdGEuY29uZmlnIGFzIENlbnRyYWxSb3V0ZXJOb2RlRGF0YVsnY29uZmlnJ107XG4gIFxuICAvLyBHZXQgY29ubmVjdGVkIEFJIHByb3ZpZGVyc1xuICBjb25zdCBjb25uZWN0ZWRQcm92aWRlcnMgPSBlZGdlc1xuICAgIC5maWx0ZXIoZWRnZSA9PiBlZGdlLnRhcmdldCA9PT0gaWQgJiYgZWRnZS50YXJnZXRIYW5kbGUgPT09ICdwcm92aWRlcnMnKVxuICAgIC5tYXAoZWRnZSA9PiB7XG4gICAgICBjb25zdCBzb3VyY2VOb2RlID0gbm9kZXMuZmluZChub2RlID0+IG5vZGUuaWQgPT09IGVkZ2Uuc291cmNlKTtcbiAgICAgIGlmIChzb3VyY2VOb2RlICYmIHNvdXJjZU5vZGUudHlwZSA9PT0gJ3Byb3ZpZGVyJykge1xuICAgICAgICBjb25zdCBwcm92aWRlckNvbmZpZyA9IHNvdXJjZU5vZGUuZGF0YS5jb25maWcgYXMgYW55O1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiBlZGdlLnNvdXJjZSxcbiAgICAgICAgICBuYW1lOiBwcm92aWRlckNvbmZpZz8ucHJvdmlkZXJJZCB8fCAnVW5rbm93biBQcm92aWRlcicsXG4gICAgICAgICAgbW9kZWw6IHByb3ZpZGVyQ29uZmlnPy5tb2RlbElkIHx8ICdVbmtub3duIE1vZGVsJ1xuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSlcbiAgICAuZmlsdGVyKEJvb2xlYW4pO1xuXG4gIC8vIEdldCBjb25uZWN0ZWQgdmlzaW9uIG5vZGVzXG4gIGNvbnN0IGNvbm5lY3RlZFZpc2lvbiA9IGVkZ2VzXG4gICAgLmZpbHRlcihlZGdlID0+IGVkZ2UudGFyZ2V0ID09PSBpZCAmJiBlZGdlLnRhcmdldEhhbmRsZSA9PT0gJ3Zpc2lvbicpXG4gICAgLm1hcChlZGdlID0+IHtcbiAgICAgIGNvbnN0IHNvdXJjZU5vZGUgPSBub2Rlcy5maW5kKG5vZGUgPT4gbm9kZS5pZCA9PT0gZWRnZS5zb3VyY2UpO1xuICAgICAgaWYgKHNvdXJjZU5vZGUgJiYgc291cmNlTm9kZS50eXBlID09PSAndmlzaW9uJykge1xuICAgICAgICBjb25zdCB2aXNpb25Db25maWcgPSBzb3VyY2VOb2RlLmRhdGEuY29uZmlnIGFzIGFueTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpZDogZWRnZS5zb3VyY2UsXG4gICAgICAgICAgbmFtZTogdmlzaW9uQ29uZmlnPy5wcm92aWRlcklkIHx8ICdVbmtub3duIFZpc2lvbiBQcm92aWRlcicsXG4gICAgICAgICAgbW9kZWw6IHZpc2lvbkNvbmZpZz8ubW9kZWxJZCB8fCAnVW5rbm93biBNb2RlbCdcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0pXG4gICAgLmZpbHRlcihCb29sZWFuKTtcblxuICAvLyBDaGVjayBpZiBjbGFzc2lmaWVyIGlzIGNvbm5lY3RlZFxuICBjb25zdCBoYXNDbGFzc2lmaWVyID0gZWRnZXMuc29tZShlZGdlID0+IFxuICAgIGVkZ2UudGFyZ2V0ID09PSBpZCAmJiBcbiAgICBlZGdlLnRhcmdldEhhbmRsZSA9PT0gJ2NsYXNzaWZpZXInICYmXG4gICAgbm9kZXMuZmluZChub2RlID0+IG5vZGUuaWQgPT09IGVkZ2Uuc291cmNlICYmIG5vZGUudHlwZSA9PT0gJ2NsYXNzaWZpZXInKVxuICApO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgey8qIE11bHRpcGxlIElucHV0IEhhbmRsZXMgKi99XG4gICAgICBcbiAgICAgIHsvKiBBSSBQcm92aWRlcnMgSW5wdXQgKi99XG4gICAgICA8SGFuZGxlXG4gICAgICAgIHR5cGU9XCJ0YXJnZXRcIlxuICAgICAgICBwb3NpdGlvbj17UG9zaXRpb24uTGVmdH1cbiAgICAgICAgaWQ9XCJwcm92aWRlcnNcIlxuICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGJvcmRlci0yIGJvcmRlci1ibHVlLTUwMCBiZy1ibHVlLTcwMCBob3Zlcjpib3JkZXItYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICBzdHlsZT17eyBsZWZ0OiAtMTIsIHRvcDogJzIwJScgfX1cbiAgICAgIC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRleHQteHMgdGV4dC1ibHVlLTIwMCBmb250LW1lZGl1bSBwb2ludGVyLWV2ZW50cy1ub25lXCIgc3R5bGU9e3sgbGVmdDogLTgwLCB0b3A6ICcxNSUnIH19PlxuICAgICAgICBBSSBQcm92aWRlcnNcbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVmlzaW9uIElucHV0ICovfVxuICAgICAgPEhhbmRsZVxuICAgICAgICB0eXBlPVwidGFyZ2V0XCJcbiAgICAgICAgcG9zaXRpb249e1Bvc2l0aW9uLkxlZnR9XG4gICAgICAgIGlkPVwidmlzaW9uXCJcbiAgICAgICAgY2xhc3NOYW1lPVwidy02IGgtNiBib3JkZXItMiBib3JkZXItcHVycGxlLTUwMCBiZy1wdXJwbGUtNzAwIGhvdmVyOmJvcmRlci1wdXJwbGUtNDAwIGhvdmVyOmJnLXB1cnBsZS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICBzdHlsZT17eyBsZWZ0OiAtMTIsIHRvcDogJzUwJScgfX1cbiAgICAgIC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRleHQteHMgdGV4dC1wdXJwbGUtMjAwIGZvbnQtbWVkaXVtIHBvaW50ZXItZXZlbnRzLW5vbmVcIiBzdHlsZT17eyBsZWZ0OiAtODAsIHRvcDogJzQ1JScgfX0+XG4gICAgICAgIFZpc2lvbiBNb2RlbHNcbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ2xhc3NpZmllciBJbnB1dCAqL31cbiAgICAgIDxIYW5kbGVcbiAgICAgICAgdHlwZT1cInRhcmdldFwiXG4gICAgICAgIHBvc2l0aW9uPXtQb3NpdGlvbi5MZWZ0fVxuICAgICAgICBpZD1cImNsYXNzaWZpZXJcIlxuICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGJvcmRlci0yIGJvcmRlci1ncmVlbi01MDAgYmctZ3JlZW4tNzAwIGhvdmVyOmJvcmRlci1ncmVlbi00MDAgaG92ZXI6YmctZ3JlZW4tNDAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgc3R5bGU9e3sgbGVmdDogLTEyLCB0b3A6ICc4MCUnIH19XG4gICAgICAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0ZXh0LXhzIHRleHQtZ3JlZW4tMjAwIGZvbnQtbWVkaXVtIHBvaW50ZXItZXZlbnRzLW5vbmVcIiBzdHlsZT17eyBsZWZ0OiAtODAsIHRvcDogJzc1JScgfX0+XG4gICAgICAgIENsYXNzaWZpZXJcbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGF0YSBJbnB1dCAqL31cbiAgICAgIDxIYW5kbGVcbiAgICAgICAgdHlwZT1cInRhcmdldFwiXG4gICAgICAgIHBvc2l0aW9uPXtQb3NpdGlvbi5Ub3B9XG4gICAgICAgIGlkPVwiaW5wdXRcIlxuICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGJvcmRlci0yIGJvcmRlci1ncmF5LTUwMCBiZy1ncmF5LTcwMCBob3Zlcjpib3JkZXItWyNmZjZiMzVdIGhvdmVyOmJnLVsjZmY2YjM1XSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgIHN0eWxlPXt7IHRvcDogLTEyIH19XG4gICAgICAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0ZXh0LXhzIHRleHQtZ3JheS0zMDAgZm9udC1tZWRpdW0gcG9pbnRlci1ldmVudHMtbm9uZVwiIHN0eWxlPXt7IHRvcDogLTMwLCBsZWZ0OiAnNDUlJyB9fT5cbiAgICAgICAgSW5wdXRcbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogT3V0cHV0IEhhbmRsZSAqL31cbiAgICAgIDxIYW5kbGVcbiAgICAgICAgdHlwZT1cInNvdXJjZVwiXG4gICAgICAgIHBvc2l0aW9uPXtQb3NpdGlvbi5SaWdodH1cbiAgICAgICAgaWQ9XCJvdXRwdXRcIlxuICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGJvcmRlci0yIGJvcmRlci1bI2ZmNmIzNV0gYmctWyNmZjZiMzVdIGhvdmVyOmJvcmRlci1vcmFuZ2UtNDAwIGhvdmVyOmJnLW9yYW5nZS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICBzdHlsZT17eyByaWdodDogLTEyIH19XG4gICAgICAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0ZXh0LXhzIHRleHQtb3JhbmdlLTIwMCBmb250LW1lZGl1bSBwb2ludGVyLWV2ZW50cy1ub25lXCIgc3R5bGU9e3sgcmlnaHQ6IC02MCwgdG9wOiAnNDUlJyB9fT5cbiAgICAgICAgT3V0cHV0XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE5vZGUgQm9keSAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgbWluLXctWzI4MHB4XSByb3VuZGVkLWxnIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgIGRhdGEuaGFzRXJyb3JcbiAgICAgICAgICAgID8gJ2JvcmRlci1yZWQtNTAwIGJnLXJlZC05MDAvMjAnXG4gICAgICAgICAgICA6IGRhdGEuaXNDb25maWd1cmVkXG4gICAgICAgICAgICA/ICdib3JkZXItZ3JheS02MDAgYmctZ3JheS04MDAvOTAnXG4gICAgICAgICAgICA6ICdib3JkZXIteWVsbG93LTUwMCBiZy15ZWxsb3ctOTAwLzIwJ1xuICAgICAgICB9IGJhY2tkcm9wLWJsdXItc20gc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bGB9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgYm9yZGVyQ29sb3I6IGRhdGEuaGFzRXJyb3IgPyAnI2VmNDQ0NCcgOiBkYXRhLmlzQ29uZmlndXJlZCA/ICcjZmY2YjM1JyA6ICcjZWFiMzA4J1xuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMyByb3VuZGVkLXQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIlxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBkYXRhLmhhc0Vycm9yIFxuICAgICAgICAgICAgICA/ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDIzOSwgNjgsIDY4LCAwLjIpLCByZ2JhKDIzOSwgNjgsIDY4LCAwLjEpKSdcbiAgICAgICAgICAgICAgOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmIzNTIwLCAjZmY2YjM1MTApJ1xuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBkYXRhLmhhc0Vycm9yID8gJyNlZjQ0NDQyMCcgOiAnI2ZmNmIzNTIwJyxcbiAgICAgICAgICAgICAgY29sb3I6IGRhdGEuaGFzRXJyb3IgPyAnI2VmNDQ0NCcgOiAnI2ZmNmIzNSdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPENwdUNoaXBJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGUgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICBDZW50cmFsIFJvdXRlclxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+XG4gICAgICAgICAgICAgIFNtYXJ0IHJvdXRpbmcgaHViIGZvciBBSSBwcm92aWRlcnNcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBTdGF0dXMgSW5kaWNhdG9ycyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICB7aGFzQ2xhc3NpZmllciAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsXCIgdGl0bGU9XCJDbGFzc2lmaWVyIENvbm5lY3RlZFwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge2Nvbm5lY3RlZFByb3ZpZGVycy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbFwiIHRpdGxlPXtgJHtjb25uZWN0ZWRQcm92aWRlcnMubGVuZ3RofSBBSSBQcm92aWRlcnNgfSAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtjb25uZWN0ZWRWaXNpb24ubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1wdXJwbGUtNTAwIHJvdW5kZWQtZnVsbFwiIHRpdGxlPXtgJHtjb25uZWN0ZWRWaXNpb24ubGVuZ3RofSBWaXNpb24gTW9kZWxzYH0gLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMyBzcGFjZS15LTNcIj5cbiAgICAgICAgICB7LyogQ29ubmVjdGVkIEFJIFByb3ZpZGVycyAqL31cbiAgICAgICAgICB7Y29ubmVjdGVkUHJvdmlkZXJzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5BSSBQcm92aWRlcnMgKHtjb25uZWN0ZWRQcm92aWRlcnMubGVuZ3RofSk6PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICB7Y29ubmVjdGVkUHJvdmlkZXJzLm1hcCgocHJvdmlkZXI6IGFueSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAga2V5PXtwcm92aWRlci5pZH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1ibHVlLTkwMC8zMCB0ZXh0LWJsdWUtMzAwIHB4LTIgcHktMC41IHJvdW5kZWQtZnVsbCBib3JkZXIgYm9yZGVyLWJsdWUtNzAwLzMwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3Byb3ZpZGVyLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBDb25uZWN0ZWQgVmlzaW9uIE1vZGVscyAqL31cbiAgICAgICAgICB7Y29ubmVjdGVkVmlzaW9uLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5WaXNpb24gTW9kZWxzICh7Y29ubmVjdGVkVmlzaW9uLmxlbmd0aH0pOjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAge2Nvbm5lY3RlZFZpc2lvbi5tYXAoKHZpc2lvbjogYW55KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICBrZXk9e3Zpc2lvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1wdXJwbGUtOTAwLzMwIHRleHQtcHVycGxlLTMwMCBweC0yIHB5LTAuNSByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci1wdXJwbGUtNzAwLzMwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3Zpc2lvbi5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogUm91dGluZyBTdHJhdGVneSAqL31cbiAgICAgICAgICB7Y29uZmlnPy5yb3V0aW5nU3RyYXRlZ3kgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS0zMDAgYmctZ3JheS03MDAvNTAgcHgtMiBweS0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgU3RyYXRlZ3k6IHtjb25maWcucm91dGluZ1N0cmF0ZWd5LnJlcGxhY2UoJ18nLCAnICcpLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIFN0YXR1cyBNZXNzYWdlcyAqL31cbiAgICAgICAgICB7IWhhc0NsYXNzaWZpZXIgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQteWVsbG93LTMwMCBiZy15ZWxsb3ctOTAwLzIwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgIOKaoO+4jyBDb25uZWN0IGNsYXNzaWZpZXIgZm9yIHNtYXJ0IHJvdXRpbmdcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAgXG4gICAgICAgICAge2Nvbm5lY3RlZFByb3ZpZGVycy5sZW5ndGggPT09IDAgJiYgY29ubmVjdGVkVmlzaW9uLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtMzAwIGJnLXJlZC05MDAvMjAgcHgtMiBweS0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAg4p2MIE5vIEFJIHByb3ZpZGVycyBjb25uZWN0ZWRcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7KGNvbm5lY3RlZFByb3ZpZGVycy5sZW5ndGggPiAwIHx8IGNvbm5lY3RlZFZpc2lvbi5sZW5ndGggPiAwKSAmJiBoYXNDbGFzc2lmaWVyICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTMwMCBiZy1ncmVlbi05MDAvMjAgcHgtMiBweS0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAg4pyFIFJlYWR5IGZvciBzbWFydCByb3V0aW5nXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkNwdUNoaXBJY29uIiwidXNlRWRnZXMiLCJ1c2VOb2RlcyIsIkhhbmRsZSIsIlBvc2l0aW9uIiwiQ2VudHJhbFJvdXRlck5vZGUiLCJkYXRhIiwiaWQiLCJlZGdlcyIsIm5vZGVzIiwiY29uZmlnIiwiY29ubmVjdGVkUHJvdmlkZXJzIiwiZmlsdGVyIiwiZWRnZSIsInRhcmdldCIsInRhcmdldEhhbmRsZSIsIm1hcCIsInNvdXJjZU5vZGUiLCJmaW5kIiwibm9kZSIsInNvdXJjZSIsInR5cGUiLCJwcm92aWRlckNvbmZpZyIsIm5hbWUiLCJwcm92aWRlcklkIiwibW9kZWwiLCJtb2RlbElkIiwiQm9vbGVhbiIsImNvbm5lY3RlZFZpc2lvbiIsInZpc2lvbkNvbmZpZyIsImhhc0NsYXNzaWZpZXIiLCJzb21lIiwiZGl2IiwiY2xhc3NOYW1lIiwicG9zaXRpb24iLCJMZWZ0Iiwic3R5bGUiLCJsZWZ0IiwidG9wIiwiVG9wIiwiUmlnaHQiLCJyaWdodCIsImhhc0Vycm9yIiwiaXNDb25maWd1cmVkIiwiYm9yZGVyQ29sb3IiLCJiYWNrZ3JvdW5kIiwiYmFja2dyb3VuZENvbG9yIiwiY29sb3IiLCJ0aXRsZSIsImxlbmd0aCIsInByb3ZpZGVyIiwic3BhbiIsInZpc2lvbiIsInJvdXRpbmdTdHJhdGVneSIsInJlcGxhY2UiLCJ0b1VwcGVyQ2FzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx\n"));

/***/ })

});