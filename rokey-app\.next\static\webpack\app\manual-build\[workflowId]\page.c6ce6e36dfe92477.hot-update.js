"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, hasToolsInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', toolsInputLabel = 'Tools', className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-gray-800/90 border border-gray-600 rounded text-xs text-gray-300 opacity-80 hover:opacity-100 transition-opacity pointer-events-none\",\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 transform -translate-y-1/2 flex items-center z-10\",\n                style: {\n                    top: '30%'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-4 h-4 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-purple-900/95 border border-purple-600 rounded text-xs text-purple-200 font-medium shadow-lg\",\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this),\n            hasToolsInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 transform -translate-y-1/2 flex items-center z-10\",\n                style: {\n                    top: '70%'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"tools\",\n                        className: \"w-4 h-4 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-green-900/95 border border-green-600 rounded text-xs text-green-200 font-medium shadow-lg\",\n                        children: toolsInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    (hasInput || hasRoleInput) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -left-16 top-0 h-full flex flex-col justify-center space-y-2 pointer-events-none\",\n                        children: [\n                            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-xs text-purple-300 bg-purple-900/90 border border-purple-700/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Role\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-0.5 bg-purple-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this),\n                            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-xs text-gray-300 bg-gray-800/90 border border-gray-600/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-0.5 bg-gray-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    right: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});