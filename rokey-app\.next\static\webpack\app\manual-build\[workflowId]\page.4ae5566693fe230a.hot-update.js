"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/ContextMenu */ \"(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_10__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n            setContextMenu(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const onNodeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeContextMenu]\": (event, node)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: node.id,\n                type: 'node',\n                nodeType: node.type,\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeContextMenu]\"], []);\n    const onEdgeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onEdgeContextMenu]\": (event, edge)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: edge.id,\n                type: 'edge',\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onEdgeContextMenu]\"], []);\n    const handleDeleteNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nodeId)=>{\n            // Don't delete core nodes\n            const coreNodes = [\n                'user-request',\n                'classifier',\n                'output'\n            ];\n            if (coreNodes.includes(nodeId)) return;\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nds)=>nds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (node)=>node.id !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (edge)=>edge.source !== nodeId && edge.target !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setIsDirty(true);\n            // Close config panel if deleted node was selected\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"], [\n        selectedNode,\n        setNodes,\n        setEdges\n    ]);\n    const handleDeleteEdge = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edgeId)=>{\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edge)=>edge.id !== edgeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"], [\n        setEdges\n    ]);\n    const handleDuplicateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nodeId)=>{\n            const nodeToDuplicate = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\"]);\n            if (!nodeToDuplicate) return;\n            const newNode = {\n                ...nodeToDuplicate,\n                id: \"\".concat(nodeToDuplicate.type, \"-\").concat(Date.now()),\n                position: {\n                    x: nodeToDuplicate.position.x + 50,\n                    y: nodeToDuplicate.position.y + 50\n                },\n                data: {\n                    ...nodeToDuplicate.data,\n                    label: \"\".concat(nodeToDuplicate.data.label, \" Copy\")\n                }\n            };\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nds)=>[\n                        ...nds,\n                        newNode\n                    ]\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"], [\n        nodes,\n        setNodes\n    ]);\n    const handleConfigureNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleConfigureNode]\": (nodeId)=>{\n            const node = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleConfigureNode].node\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleConfigureNode].node\"]);\n            if (node) {\n                setSelectedNode(node);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleConfigureNode]\"], [\n        nodes\n    ]);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            // TODO: Implement save new workflow\n            console.log('Saving new workflow:', name);\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                // TODO: Implement update workflow API call\n                console.log('Updating workflow:', workflowId);\n                setIsDirty(false);\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleExecute = async ()=>{\n        // TODO: Implement workflow execution\n        console.log('Executing workflow');\n    };\n    const handleAddNode = (nodeType, position)=>{\n        let defaultConfig = {};\n        let isConfigured = true;\n        // Set proper default config for specific node types\n        if (nodeType === 'provider') {\n            defaultConfig = {\n                providerId: '',\n                modelId: '',\n                apiKey: '',\n                parameters: {\n                    temperature: 1.0,\n                    maxTokens: undefined,\n                    topP: undefined,\n                    frequencyPenalty: undefined,\n                    presencePenalty: undefined\n                }\n            };\n            isConfigured = false;\n        } else if (nodeType === 'centralRouter') {\n            defaultConfig = {\n                routingStrategy: 'smart',\n                fallbackProvider: '',\n                maxRetries: 3,\n                timeout: 30000,\n                enableCaching: true,\n                debugMode: false\n            };\n            isConfigured = true;\n        }\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType === 'centralRouter' ? 'Central Router' : nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: defaultConfig,\n                isConfigured,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 297,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#040716] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                workflow: workflow,\n                isDirty: isDirty,\n                isSaving: isSaving,\n                onSave: handleSave,\n                onExecute: handleExecute,\n                onBack: ()=>router.push('/manual-build')\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddNode: handleAddNode\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative manual-build-canvas\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.ReactFlow, {\n                                nodes: nodes,\n                                edges: edges,\n                                onNodesChange: onNodesChange,\n                                onEdgesChange: onEdgesChange,\n                                onConnect: onConnect,\n                                onNodeClick: onNodeClick,\n                                onNodeContextMenu: onNodeContextMenu,\n                                onEdgeContextMenu: onEdgeContextMenu,\n                                onPaneClick: onPaneClick,\n                                nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__.nodeTypes,\n                                fitView: true,\n                                className: \"bg-[#040716]\",\n                                defaultViewport: {\n                                    x: 0,\n                                    y: 0,\n                                    zoom: 0.8\n                                },\n                                connectionLineStyle: {\n                                    stroke: '#ff6b35',\n                                    strokeWidth: 2\n                                },\n                                defaultEdgeOptions: {\n                                    style: {\n                                        stroke: '#ff6b35',\n                                        strokeWidth: 2\n                                    },\n                                    type: 'smoothstep',\n                                    animated: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Background, {\n                                        color: \"#1f2937\",\n                                        gap: 20,\n                                        size: 1,\n                                        variant: \"dots\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Controls, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        showInteractive: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.MiniMap, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        nodeColor: \"#ff6b35\",\n                                        maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this),\n                            contextMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                id: contextMenu.id,\n                                type: contextMenu.type,\n                                nodeType: contextMenu.nodeType,\n                                top: contextMenu.y,\n                                left: contextMenu.x,\n                                onClose: ()=>setContextMenu(null),\n                                onDelete: contextMenu.type === 'node' ? handleDeleteNode : handleDeleteEdge,\n                                onDuplicate: contextMenu.type === 'node' ? handleDuplicateNode : undefined,\n                                onConfigure: contextMenu.type === 'node' ? handleConfigureNode : undefined,\n                                onDisconnect: contextMenu.type === 'edge' ? handleDeleteEdge : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        node: selectedNode,\n                        onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                        onClose: ()=>setSelectedNode(null)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"GiKoqOcAXo1NotOy4cq/1dWBc14=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbWFudWFsLWJ1aWxkL1t3b3JrZmxvd0lkXS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUQ7QUFDRjtBQUMyRTtBQUM1RjtBQUdrQztBQUNSO0FBQ1E7QUFDUjtBQUNKO0FBTTdDLFNBQVNpQixtQkFBbUIsS0FBbUM7UUFBbkMsRUFBRUMsTUFBTSxFQUEyQixHQUFuQzs7SUFDekMsTUFBTUMsaUJBQWlCaEIsMERBQVNBO0lBQ2hDLE1BQU1pQixTQUFTaEIsMERBQVNBO0lBQ3hCLE1BQU1pQixhQUFhRiwyQkFBQUEscUNBQUFBLGVBQWdCRSxVQUFVO0lBRTdDLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHdkIsK0NBQVFBLENBQTZCO0lBQ3JFLE1BQU0sQ0FBQ3dCLE9BQU9DLFVBQVVDLGNBQWMsR0FBR2pCLDREQUFhQSxDQUFDLEVBQUU7SUFDekQsTUFBTSxDQUFDa0IsT0FBT0MsVUFBVUMsY0FBYyxHQUFHbkIsNERBQWFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUNvQixjQUFjQyxnQkFBZ0IsR0FBRy9CLCtDQUFRQSxDQUFzQjtJQUN0RSxNQUFNLENBQUNnQyxXQUFXQyxhQUFhLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUduQywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNvQyxTQUFTQyxXQUFXLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQyxhQUFhQyxlQUFlLEdBQUd2QywrQ0FBUUEsQ0FNcEM7SUFFVixxQkFBcUI7SUFDckJDLGdEQUFTQTt3Q0FBQztZQUNSLElBQUlvQixlQUFlLE9BQU87Z0JBQ3hCbUI7WUFDRixPQUFPO2dCQUNMQyxhQUFhcEI7WUFDZjtRQUNGO3VDQUFHO1FBQUNBO0tBQVc7SUFFZixNQUFNbUIsd0JBQXdCO1FBQzVCLElBQUk7WUFDRix3Q0FBd0M7WUFDeEMsTUFBTUUsZUFBK0I7Z0JBQ25DO29CQUNFQyxJQUFJO29CQUNKQyxNQUFNO29CQUNOQyxVQUFVO3dCQUFFQyxHQUFHO3dCQUFJQyxHQUFHO29CQUFJO29CQUMxQkMsTUFBTTt3QkFDSkMsT0FBTzt3QkFDUEMsUUFBUSxDQUFDO3dCQUNUQyxjQUFjO3dCQUNkQyxhQUFhO29CQUNmO2dCQUNGO2dCQUNBO29CQUNFVCxJQUFJO29CQUNKQyxNQUFNO29CQUNOQyxVQUFVO3dCQUFFQyxHQUFHO3dCQUFLQyxHQUFHO29CQUFJO29CQUMzQkMsTUFBTTt3QkFDSkMsT0FBTzt3QkFDUEMsUUFBUSxDQUFDO3dCQUNUQyxjQUFjO3dCQUNkQyxhQUFhO29CQUNmO2dCQUNGO2dCQUNBO29CQUNFVCxJQUFJO29CQUNKQyxNQUFNO29CQUNOQyxVQUFVO3dCQUFFQyxHQUFHO3dCQUFLQyxHQUFHO29CQUFJO29CQUMzQkMsTUFBTTt3QkFDSkMsT0FBTzt3QkFDUEMsUUFBUSxDQUFDO3dCQUNUQyxjQUFjO3dCQUNkQyxhQUFhO29CQUNmO2dCQUNGO2FBQ0Q7WUFFRCxNQUFNQyxlQUErQjtnQkFDbkM7b0JBQ0VWLElBQUk7b0JBQ0pXLFFBQVE7b0JBQ1JDLFFBQVE7b0JBQ1JYLE1BQU07b0JBQ05ZLFVBQVU7Z0JBQ1o7YUFDRDtZQUVEL0IsU0FBU2lCO1lBQ1RkLFNBQVN5QjtZQUNUcEIsYUFBYTtRQUNmLEVBQUUsT0FBT3dCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcER4QixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1RLGVBQWUsT0FBT0U7UUFDMUIsSUFBSTtZQUNGLDRDQUE0QztZQUM1Q2UsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmhCO1lBQ2pDVixhQUFhO1FBQ2YsRUFBRSxPQUFPd0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQ3hCLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTJCLFlBQVkxRCxrREFBV0E7cURBQzNCLENBQUNnQjtZQUNDLE1BQU0yQyxVQUFnQjtnQkFDcEIsR0FBRzNDLE1BQU07Z0JBQ1R5QixJQUFJLElBQXFCLE9BQWpCaEIsTUFBTW1DLE1BQU0sR0FBRztnQkFDdkJsQixNQUFNO2dCQUNOWSxVQUFVO1lBQ1o7WUFDQTVCOzZEQUFTLENBQUNtQyxNQUFRcEQsdURBQU9BLENBQUNrRCxTQUFTRTs7WUFDbkMxQixXQUFXO1FBQ2I7b0RBQ0E7UUFBQ1YsTUFBTW1DLE1BQU07UUFBRWxDO0tBQVM7SUFHMUIsTUFBTW9DLGNBQWM5RCxrREFBV0E7dURBQUMsQ0FBQytELE9BQXlCQztZQUN4RG5DLGdCQUFnQm1DO1FBQ2xCO3NEQUFHLEVBQUU7SUFFTCxNQUFNQyxjQUFjakUsa0RBQVdBO3VEQUFDO1lBQzlCNkIsZ0JBQWdCO1lBQ2hCUSxlQUFlO1FBQ2pCO3NEQUFHLEVBQUU7SUFFTCxNQUFNNkIsb0JBQW9CbEUsa0RBQVdBOzZEQUFDLENBQUMrRCxPQUF5QkM7WUFDOURELE1BQU1JLGNBQWM7WUFDcEI5QixlQUFlO2dCQUNiSSxJQUFJdUIsS0FBS3ZCLEVBQUU7Z0JBQ1hDLE1BQU07Z0JBQ04wQixVQUFVSixLQUFLdEIsSUFBSTtnQkFDbkJFLEdBQUdtQixNQUFNTSxPQUFPO2dCQUNoQnhCLEdBQUdrQixNQUFNTyxPQUFPO1lBQ2xCO1FBQ0Y7NERBQUcsRUFBRTtJQUVMLE1BQU1DLG9CQUFvQnZFLGtEQUFXQTs2REFBQyxDQUFDK0QsT0FBeUJTO1lBQzlEVCxNQUFNSSxjQUFjO1lBQ3BCOUIsZUFBZTtnQkFDYkksSUFBSStCLEtBQUsvQixFQUFFO2dCQUNYQyxNQUFNO2dCQUNORSxHQUFHbUIsTUFBTU0sT0FBTztnQkFDaEJ4QixHQUFHa0IsTUFBTU8sT0FBTztZQUNsQjtRQUNGOzREQUFHLEVBQUU7SUFFTCxNQUFNRyxtQkFBbUJ6RSxrREFBV0E7NERBQUMsQ0FBQzBFO1lBQ3BDLDBCQUEwQjtZQUMxQixNQUFNQyxZQUFZO2dCQUFDO2dCQUFnQjtnQkFBYzthQUFTO1lBQzFELElBQUlBLFVBQVVDLFFBQVEsQ0FBQ0YsU0FBUztZQUVoQ25EO29FQUFTLENBQUNzRCxNQUFRQSxJQUFJQyxNQUFNOzRFQUFDLENBQUNkLE9BQVNBLEtBQUt2QixFQUFFLEtBQUtpQzs7O1lBQ25EaEQ7b0VBQVMsQ0FBQ21DLE1BQVFBLElBQUlpQixNQUFNOzRFQUFDLENBQUNOLE9BQVNBLEtBQUtwQixNQUFNLEtBQUtzQixVQUFVRixLQUFLbkIsTUFBTSxLQUFLcUI7OztZQUNqRnZDLFdBQVc7WUFFWCxrREFBa0Q7WUFDbEQsSUFBSVAsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjYSxFQUFFLE1BQUtpQyxRQUFRO2dCQUMvQjdDLGdCQUFnQjtZQUNsQjtRQUNGOzJEQUFHO1FBQUNEO1FBQWNMO1FBQVVHO0tBQVM7SUFFckMsTUFBTXFELG1CQUFtQi9FLGtEQUFXQTs0REFBQyxDQUFDZ0Y7WUFDcEN0RDtvRUFBUyxDQUFDbUMsTUFBUUEsSUFBSWlCLE1BQU07NEVBQUMsQ0FBQ04sT0FBU0EsS0FBSy9CLEVBQUUsS0FBS3VDOzs7WUFDbkQ3QyxXQUFXO1FBQ2I7MkRBQUc7UUFBQ1Q7S0FBUztJQUViLE1BQU11RCxzQkFBc0JqRixrREFBV0E7K0RBQUMsQ0FBQzBFO1lBQ3ZDLE1BQU1RLGtCQUFrQjVELE1BQU02RCxJQUFJO3VGQUFDQyxDQUFBQSxJQUFLQSxFQUFFM0MsRUFBRSxLQUFLaUM7O1lBQ2pELElBQUksQ0FBQ1EsaUJBQWlCO1lBRXRCLE1BQU1HLFVBQXdCO2dCQUM1QixHQUFHSCxlQUFlO2dCQUNsQnpDLElBQUksR0FBMkI2QyxPQUF4QkosZ0JBQWdCeEMsSUFBSSxFQUFDLEtBQWMsT0FBWDRDLEtBQUtDLEdBQUc7Z0JBQ3ZDNUMsVUFBVTtvQkFDUkMsR0FBR3NDLGdCQUFnQnZDLFFBQVEsQ0FBQ0MsQ0FBQyxHQUFHO29CQUNoQ0MsR0FBR3FDLGdCQUFnQnZDLFFBQVEsQ0FBQ0UsQ0FBQyxHQUFHO2dCQUNsQztnQkFDQUMsTUFBTTtvQkFDSixHQUFHb0MsZ0JBQWdCcEMsSUFBSTtvQkFDdkJDLE9BQU8sR0FBOEIsT0FBM0JtQyxnQkFBZ0JwQyxJQUFJLENBQUNDLEtBQUssRUFBQztnQkFDdkM7WUFDRjtZQUVBeEI7dUVBQVMsQ0FBQ3NELE1BQVE7MkJBQUlBO3dCQUFLUTtxQkFBUTs7WUFDbkNsRCxXQUFXO1FBQ2I7OERBQUc7UUFBQ2I7UUFBT0M7S0FBUztJQUVwQixNQUFNaUUsc0JBQXNCeEYsa0RBQVdBOytEQUFDLENBQUMwRTtZQUN2QyxNQUFNVixPQUFPMUMsTUFBTTZELElBQUk7NEVBQUNDLENBQUFBLElBQUtBLEVBQUUzQyxFQUFFLEtBQUtpQzs7WUFDdEMsSUFBSVYsTUFBTTtnQkFDUm5DLGdCQUFnQm1DO1lBQ2xCO1FBQ0Y7OERBQUc7UUFBQzFDO0tBQU07SUFFVixNQUFNbUUsYUFBYTtRQUNqQixJQUFJLENBQUNyRSxZQUFZRCxlQUFlLE9BQU87WUFDckMsb0NBQW9DO1lBQ3BDLE1BQU11RSxPQUFPQyxPQUFPO1lBQ3BCLElBQUksQ0FBQ0QsTUFBTTtZQUVYLG9DQUFvQztZQUNwQ2xDLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JpQztRQUN0QyxPQUFPO1lBQ0wsMkJBQTJCO1lBQzNCekQsWUFBWTtZQUNaLElBQUk7Z0JBQ0YsMkNBQTJDO2dCQUMzQ3VCLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0J0QztnQkFDbENnQixXQUFXO1lBQ2IsRUFBRSxPQUFPb0IsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDNUMsU0FBVTtnQkFDUnRCLFlBQVk7WUFDZDtRQUNGO0lBQ0Y7SUFFQSxNQUFNMkQsZ0JBQWdCO1FBQ3BCLHFDQUFxQztRQUNyQ3BDLFFBQVFDLEdBQUcsQ0FBQztJQUNkO0lBRUEsTUFBTW9DLGdCQUFnQixDQUFDekIsVUFBa0J6QjtRQUN2QyxJQUFJbUQsZ0JBQWdCLENBQUM7UUFDckIsSUFBSTdDLGVBQWU7UUFFbkIsb0RBQW9EO1FBQ3BELElBQUltQixhQUFhLFlBQVk7WUFDM0IwQixnQkFBZ0I7Z0JBQ2RDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLFFBQVE7Z0JBQ1JDLFlBQVk7b0JBQ1ZDLGFBQWE7b0JBQ2JDLFdBQVdDO29CQUNYQyxNQUFNRDtvQkFDTkUsa0JBQWtCRjtvQkFDbEJHLGlCQUFpQkg7Z0JBQ25CO1lBQ0Y7WUFDQXBELGVBQWU7UUFDakIsT0FBTyxJQUFJbUIsYUFBYSxpQkFBaUI7WUFDdkMwQixnQkFBZ0I7Z0JBQ2RXLGlCQUFpQjtnQkFDakJDLGtCQUFrQjtnQkFDbEJDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLGVBQWU7Z0JBQ2ZDLFdBQVc7WUFDYjtZQUNBN0QsZUFBZTtRQUNqQjtRQUVBLE1BQU1vQyxVQUF3QjtZQUM1QjVDLElBQUksR0FBZTZDLE9BQVpsQixVQUFTLEtBQWMsT0FBWGtCLEtBQUtDLEdBQUc7WUFDM0I3QyxNQUFNMEI7WUFDTnpCO1lBQ0FHLE1BQU07Z0JBQ0pDLE9BQU9xQixhQUFhLGtCQUFrQixtQkFBbUJBLFNBQVMyQyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLNUMsU0FBUzZDLEtBQUssQ0FBQztnQkFDM0dqRSxRQUFROEM7Z0JBQ1I3QztnQkFDQUMsYUFBYSxHQUFZLE9BQVRrQixVQUFTO1lBQzNCO1FBQ0Y7UUFFQTdDLFNBQVMsQ0FBQ3NELE1BQVE7bUJBQUlBO2dCQUFLUTthQUFRO1FBQ25DbEQsV0FBVztJQUNiO0lBRUEsTUFBTStFLG1CQUFtQixDQUFDeEMsUUFBZ0J5QztRQUN4QzVGLFNBQVMsQ0FBQ3NELE1BQ1JBLElBQUl1QyxHQUFHLENBQUMsQ0FBQ3BELE9BQ1BBLEtBQUt2QixFQUFFLEtBQUtpQyxTQUNSO29CQUFFLEdBQUdWLElBQUk7b0JBQUVsQixNQUFNO3dCQUFFLEdBQUdrQixLQUFLbEIsSUFBSTt3QkFBRSxHQUFHcUUsT0FBTztvQkFBQztnQkFBRSxJQUM5Q25EO1FBR1I3QixXQUFXO0lBQ2I7SUFFQSxJQUFJTCxXQUFXO1FBQ2IscUJBQ0UsOERBQUN1RjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFBYTs7Ozs7Ozs7Ozs7SUFHbEM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUM1RyxnRkFBZUE7Z0JBQ2RVLFVBQVVBO2dCQUNWYyxTQUFTQTtnQkFDVEYsVUFBVUE7Z0JBQ1Z1RixRQUFROUI7Z0JBQ1IrQixXQUFXNUI7Z0JBQ1g2QixRQUFRLElBQU12RyxPQUFPd0csSUFBSSxDQUFDOzs7Ozs7MEJBRzVCLDhEQUFDTDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUMzRyw0RUFBV0E7d0JBQUNnSCxXQUFXOUI7Ozs7OztrQ0FHeEIsOERBQUN3Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNuSCxvREFBU0E7Z0NBQ1JtQixPQUFPQTtnQ0FDUEcsT0FBT0E7Z0NBQ1BELGVBQWVBO2dDQUNmRyxlQUFlQTtnQ0FDZitCLFdBQVdBO2dDQUNYSSxhQUFhQTtnQ0FDYkksbUJBQW1CQTtnQ0FDbkJLLG1CQUFtQkE7Z0NBQ25CTixhQUFhQTtnQ0FDYm5ELFdBQVdBLHFFQUFTQTtnQ0FDcEI4RyxPQUFPO2dDQUNQTixXQUFVO2dDQUNWTyxpQkFBaUI7b0NBQUVqRixHQUFHO29DQUFHQyxHQUFHO29DQUFHaUYsTUFBTTtnQ0FBSTtnQ0FDekNDLHFCQUFxQjtvQ0FBRUMsUUFBUTtvQ0FBV0MsYUFBYTtnQ0FBRTtnQ0FDekRDLG9CQUFvQjtvQ0FDbEJDLE9BQU87d0NBQUVILFFBQVE7d0NBQVdDLGFBQWE7b0NBQUU7b0NBQzNDdkYsTUFBTTtvQ0FDTlksVUFBVTtnQ0FDWjs7a0RBRUEsOERBQUNsRCxxREFBVUE7d0NBQ1RnSSxPQUFNO3dDQUNOQyxLQUFLO3dDQUNMQyxNQUFNO3dDQUNOQyxTQUFROzs7Ozs7a0RBRVYsOERBQUNsSSxtREFBUUE7d0NBQ1BpSCxXQUFVO3dDQUNWa0IsaUJBQWlCOzs7Ozs7a0RBRW5CLDhEQUFDbEksa0RBQU9BO3dDQUNOZ0gsV0FBVTt3Q0FDVm1CLFdBQVU7d0NBQ1ZDLFdBQVU7Ozs7Ozs7Ozs7Ozs0QkFLYnRHLDZCQUNDLDhEQUFDdkIsNEVBQVdBO2dDQUNWNEIsSUFBSUwsWUFBWUssRUFBRTtnQ0FDbEJDLE1BQU1OLFlBQVlNLElBQUk7Z0NBQ3RCMEIsVUFBVWhDLFlBQVlnQyxRQUFRO2dDQUM5QnVFLEtBQUt2RyxZQUFZUyxDQUFDO2dDQUNsQitGLE1BQU14RyxZQUFZUSxDQUFDO2dDQUNuQmlHLFNBQVMsSUFBTXhHLGVBQWU7Z0NBQzlCeUcsVUFBVTFHLFlBQVlNLElBQUksS0FBSyxTQUFTK0IsbUJBQW1CTTtnQ0FDM0RnRSxhQUFhM0csWUFBWU0sSUFBSSxLQUFLLFNBQVN1QyxzQkFBc0JvQjtnQ0FDakUyQyxhQUFhNUcsWUFBWU0sSUFBSSxLQUFLLFNBQVM4QyxzQkFBc0JhO2dDQUNqRTRDLGNBQWM3RyxZQUFZTSxJQUFJLEtBQUssU0FBU3FDLG1CQUFtQnNCOzs7Ozs7Ozs7Ozs7b0JBTXBFekUsOEJBQ0MsOERBQUNoQixnRkFBZUE7d0JBQ2RvRCxNQUFNcEM7d0JBQ05zSCxVQUFVLENBQUMvQixVQUFZRCxpQkFBaUJ0RixhQUFhYSxFQUFFLEVBQUUwRTt3QkFDekQwQixTQUFTLElBQU1oSCxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU0zQztHQWhYd0JkOztRQUNDZCxzREFBU0E7UUFDakJDLHNEQUFTQTtRQUlpQkssd0RBQWFBO1FBQ2JDLHdEQUFhQTs7O0tBUGhDTyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcbWFudWFsLWJ1aWxkXFxbd29ya2Zsb3dJZF1cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBSZWFjdEZsb3csIEJhY2tncm91bmQsIENvbnRyb2xzLCBNaW5pTWFwLCB1c2VOb2Rlc1N0YXRlLCB1c2VFZGdlc1N0YXRlLCBhZGRFZGdlLCBDb25uZWN0aW9uLCBFZGdlIH0gZnJvbSAnQHh5Zmxvdy9yZWFjdCc7XG5pbXBvcnQgJ0B4eWZsb3cvcmVhY3QvZGlzdC9zdHlsZS5jc3MnO1xuXG5pbXBvcnQgeyBXb3JrZmxvd05vZGUsIFdvcmtmbG93RWRnZSwgTWFudWFsQnVpbGRXb3JrZmxvdyB9IGZyb20gJ0AvdHlwZXMvbWFudWFsQnVpbGQnO1xuaW1wb3J0IFdvcmtmbG93VG9vbGJhciBmcm9tICdAL2NvbXBvbmVudHMvbWFudWFsLWJ1aWxkL1dvcmtmbG93VG9vbGJhcic7XG5pbXBvcnQgTm9kZVBhbGV0dGUgZnJvbSAnQC9jb21wb25lbnRzL21hbnVhbC1idWlsZC9Ob2RlUGFsZXR0ZSc7XG5pbXBvcnQgTm9kZUNvbmZpZ1BhbmVsIGZyb20gJ0AvY29tcG9uZW50cy9tYW51YWwtYnVpbGQvTm9kZUNvbmZpZ1BhbmVsJztcbmltcG9ydCBDb250ZXh0TWVudSBmcm9tICdAL2NvbXBvbmVudHMvbWFudWFsLWJ1aWxkL0NvbnRleHRNZW51JztcbmltcG9ydCB7IG5vZGVUeXBlcyB9IGZyb20gJ0AvY29tcG9uZW50cy9tYW51YWwtYnVpbGQvbm9kZXMnO1xuXG5pbnRlcmZhY2UgV29ya2Zsb3dFZGl0b3JQYWdlUHJvcHMge1xuICBwYXJhbXM6IFByb21pc2U8eyB3b3JrZmxvd0lkOiBzdHJpbmcgfT47XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFdvcmtmbG93RWRpdG9yUGFnZSh7IHBhcmFtcyB9OiBXb3JrZmxvd0VkaXRvclBhZ2VQcm9wcykge1xuICBjb25zdCByZXNvbHZlZFBhcmFtcyA9IHVzZVBhcmFtcygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3Qgd29ya2Zsb3dJZCA9IHJlc29sdmVkUGFyYW1zPy53b3JrZmxvd0lkIGFzIHN0cmluZztcbiAgXG4gIGNvbnN0IFt3b3JrZmxvdywgc2V0V29ya2Zsb3ddID0gdXNlU3RhdGU8TWFudWFsQnVpbGRXb3JrZmxvdyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbm9kZXMsIHNldE5vZGVzLCBvbk5vZGVzQ2hhbmdlXSA9IHVzZU5vZGVzU3RhdGUoW10pO1xuICBjb25zdCBbZWRnZXMsIHNldEVkZ2VzLCBvbkVkZ2VzQ2hhbmdlXSA9IHVzZUVkZ2VzU3RhdGUoW10pO1xuICBjb25zdCBbc2VsZWN0ZWROb2RlLCBzZXRTZWxlY3RlZE5vZGVdID0gdXNlU3RhdGU8V29ya2Zsb3dOb2RlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2lzU2F2aW5nLCBzZXRJc1NhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0RpcnR5LCBzZXRJc0RpcnR5XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvbnRleHRNZW51LCBzZXRDb250ZXh0TWVudV0gPSB1c2VTdGF0ZTx7XG4gICAgaWQ6IHN0cmluZztcbiAgICB0eXBlOiAnbm9kZScgfCAnZWRnZSc7XG4gICAgbm9kZVR5cGU/OiBzdHJpbmc7XG4gICAgeDogbnVtYmVyO1xuICAgIHk6IG51bWJlcjtcbiAgfSB8IG51bGw+KG51bGwpO1xuXG4gIC8vIExvYWQgd29ya2Zsb3cgZGF0YVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh3b3JrZmxvd0lkID09PSAnbmV3Jykge1xuICAgICAgaW5pdGlhbGl6ZU5ld1dvcmtmbG93KCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGxvYWRXb3JrZmxvdyh3b3JrZmxvd0lkKTtcbiAgICB9XG4gIH0sIFt3b3JrZmxvd0lkXSk7XG5cbiAgY29uc3QgaW5pdGlhbGl6ZU5ld1dvcmtmbG93ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDcmVhdGUgZGVmYXVsdCBub2RlcyBmb3IgbmV3IHdvcmtmbG93XG4gICAgICBjb25zdCBkZWZhdWx0Tm9kZXM6IFdvcmtmbG93Tm9kZVtdID0gW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICd1c2VyLXJlcXVlc3QnLFxuICAgICAgICAgIHR5cGU6ICd1c2VyUmVxdWVzdCcsXG4gICAgICAgICAgcG9zaXRpb246IHsgeDogNTAsIHk6IDIwMCB9LFxuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIGxhYmVsOiAnVXNlciBSZXF1ZXN0JyxcbiAgICAgICAgICAgIGNvbmZpZzoge30sXG4gICAgICAgICAgICBpc0NvbmZpZ3VyZWQ6IHRydWUsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ1N0YXJ0aW5nIHBvaW50IGZvciB1c2VyIGlucHV0J1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnY2xhc3NpZmllcicsXG4gICAgICAgICAgdHlwZTogJ2NsYXNzaWZpZXInLFxuICAgICAgICAgIHBvc2l0aW9uOiB7IHg6IDM1MCwgeTogMjAwIH0sXG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgbGFiZWw6ICdDbGFzc2lmaWVyJyxcbiAgICAgICAgICAgIGNvbmZpZzoge30sXG4gICAgICAgICAgICBpc0NvbmZpZ3VyZWQ6IHRydWUsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ0FuYWx5emVzIGFuZCBjYXRlZ29yaXplcyB0aGUgcmVxdWVzdCdcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ291dHB1dCcsXG4gICAgICAgICAgdHlwZTogJ291dHB1dCcsXG4gICAgICAgICAgcG9zaXRpb246IHsgeDogOTUwLCB5OiAyMDAgfSxcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBsYWJlbDogJ091dHB1dCcsXG4gICAgICAgICAgICBjb25maWc6IHt9LFxuICAgICAgICAgICAgaXNDb25maWd1cmVkOiB0cnVlLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICdGaW5hbCByZXNwb25zZSB0byB0aGUgdXNlcidcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF07XG5cbiAgICAgIGNvbnN0IGRlZmF1bHRFZGdlczogV29ya2Zsb3dFZGdlW10gPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2UxJyxcbiAgICAgICAgICBzb3VyY2U6ICd1c2VyLXJlcXVlc3QnLFxuICAgICAgICAgIHRhcmdldDogJ2NsYXNzaWZpZXInLFxuICAgICAgICAgIHR5cGU6ICdzbW9vdGhzdGVwJyxcbiAgICAgICAgICBhbmltYXRlZDogdHJ1ZVxuICAgICAgICB9XG4gICAgICBdO1xuXG4gICAgICBzZXROb2RlcyhkZWZhdWx0Tm9kZXMpO1xuICAgICAgc2V0RWRnZXMoZGVmYXVsdEVkZ2VzKTtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbml0aWFsaXplIG5ldyB3b3JrZmxvdzonLCBlcnJvcik7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkV29ya2Zsb3cgPSBhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBUT0RPOiBJbXBsZW1lbnQgQVBJIGNhbGwgdG8gbG9hZCB3b3JrZmxvd1xuICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgd29ya2Zsb3c6JywgaWQpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgd29ya2Zsb3c6JywgZXJyb3IpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgb25Db25uZWN0ID0gdXNlQ2FsbGJhY2soXG4gICAgKHBhcmFtczogQ29ubmVjdGlvbikgPT4ge1xuICAgICAgY29uc3QgbmV3RWRnZTogRWRnZSA9IHtcbiAgICAgICAgLi4ucGFyYW1zLFxuICAgICAgICBpZDogYGUke2VkZ2VzLmxlbmd0aCArIDF9YCxcbiAgICAgICAgdHlwZTogJ3Ntb290aHN0ZXAnLFxuICAgICAgICBhbmltYXRlZDogdHJ1ZVxuICAgICAgfTtcbiAgICAgIHNldEVkZ2VzKChlZHMpID0+IGFkZEVkZ2UobmV3RWRnZSwgZWRzKSk7XG4gICAgICBzZXRJc0RpcnR5KHRydWUpO1xuICAgIH0sXG4gICAgW2VkZ2VzLmxlbmd0aCwgc2V0RWRnZXNdXG4gICk7XG5cbiAgY29uc3Qgb25Ob2RlQ2xpY2sgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQsIG5vZGU6IFdvcmtmbG93Tm9kZSkgPT4ge1xuICAgIHNldFNlbGVjdGVkTm9kZShub2RlKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IG9uUGFuZUNsaWNrID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkTm9kZShudWxsKTtcbiAgICBzZXRDb250ZXh0TWVudShudWxsKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IG9uTm9kZUNvbnRleHRNZW51ID0gdXNlQ2FsbGJhY2soKGV2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50LCBub2RlOiBXb3JrZmxvd05vZGUpID0+IHtcbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldENvbnRleHRNZW51KHtcbiAgICAgIGlkOiBub2RlLmlkLFxuICAgICAgdHlwZTogJ25vZGUnLFxuICAgICAgbm9kZVR5cGU6IG5vZGUudHlwZSxcbiAgICAgIHg6IGV2ZW50LmNsaWVudFgsXG4gICAgICB5OiBldmVudC5jbGllbnRZLFxuICAgIH0pO1xuICB9LCBbXSk7XG5cbiAgY29uc3Qgb25FZGdlQ29udGV4dE1lbnUgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQsIGVkZ2U6IFdvcmtmbG93RWRnZSkgPT4ge1xuICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0Q29udGV4dE1lbnUoe1xuICAgICAgaWQ6IGVkZ2UuaWQsXG4gICAgICB0eXBlOiAnZWRnZScsXG4gICAgICB4OiBldmVudC5jbGllbnRYLFxuICAgICAgeTogZXZlbnQuY2xpZW50WSxcbiAgICB9KTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZU5vZGUgPSB1c2VDYWxsYmFjaygobm9kZUlkOiBzdHJpbmcpID0+IHtcbiAgICAvLyBEb24ndCBkZWxldGUgY29yZSBub2Rlc1xuICAgIGNvbnN0IGNvcmVOb2RlcyA9IFsndXNlci1yZXF1ZXN0JywgJ2NsYXNzaWZpZXInLCAnb3V0cHV0J107XG4gICAgaWYgKGNvcmVOb2Rlcy5pbmNsdWRlcyhub2RlSWQpKSByZXR1cm47XG5cbiAgICBzZXROb2RlcygobmRzKSA9PiBuZHMuZmlsdGVyKChub2RlKSA9PiBub2RlLmlkICE9PSBub2RlSWQpKTtcbiAgICBzZXRFZGdlcygoZWRzKSA9PiBlZHMuZmlsdGVyKChlZGdlKSA9PiBlZGdlLnNvdXJjZSAhPT0gbm9kZUlkICYmIGVkZ2UudGFyZ2V0ICE9PSBub2RlSWQpKTtcbiAgICBzZXRJc0RpcnR5KHRydWUpO1xuXG4gICAgLy8gQ2xvc2UgY29uZmlnIHBhbmVsIGlmIGRlbGV0ZWQgbm9kZSB3YXMgc2VsZWN0ZWRcbiAgICBpZiAoc2VsZWN0ZWROb2RlPy5pZCA9PT0gbm9kZUlkKSB7XG4gICAgICBzZXRTZWxlY3RlZE5vZGUobnVsbCk7XG4gICAgfVxuICB9LCBbc2VsZWN0ZWROb2RlLCBzZXROb2Rlcywgc2V0RWRnZXNdKTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVFZGdlID0gdXNlQ2FsbGJhY2soKGVkZ2VJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RWRnZXMoKGVkcykgPT4gZWRzLmZpbHRlcigoZWRnZSkgPT4gZWRnZS5pZCAhPT0gZWRnZUlkKSk7XG4gICAgc2V0SXNEaXJ0eSh0cnVlKTtcbiAgfSwgW3NldEVkZ2VzXSk7XG5cbiAgY29uc3QgaGFuZGxlRHVwbGljYXRlTm9kZSA9IHVzZUNhbGxiYWNrKChub2RlSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vZGVUb0R1cGxpY2F0ZSA9IG5vZGVzLmZpbmQobiA9PiBuLmlkID09PSBub2RlSWQpO1xuICAgIGlmICghbm9kZVRvRHVwbGljYXRlKSByZXR1cm47XG5cbiAgICBjb25zdCBuZXdOb2RlOiBXb3JrZmxvd05vZGUgPSB7XG4gICAgICAuLi5ub2RlVG9EdXBsaWNhdGUsXG4gICAgICBpZDogYCR7bm9kZVRvRHVwbGljYXRlLnR5cGV9LSR7RGF0ZS5ub3coKX1gLFxuICAgICAgcG9zaXRpb246IHtcbiAgICAgICAgeDogbm9kZVRvRHVwbGljYXRlLnBvc2l0aW9uLnggKyA1MCxcbiAgICAgICAgeTogbm9kZVRvRHVwbGljYXRlLnBvc2l0aW9uLnkgKyA1MCxcbiAgICAgIH0sXG4gICAgICBkYXRhOiB7XG4gICAgICAgIC4uLm5vZGVUb0R1cGxpY2F0ZS5kYXRhLFxuICAgICAgICBsYWJlbDogYCR7bm9kZVRvRHVwbGljYXRlLmRhdGEubGFiZWx9IENvcHlgLFxuICAgICAgfVxuICAgIH07XG5cbiAgICBzZXROb2RlcygobmRzKSA9PiBbLi4ubmRzLCBuZXdOb2RlXSk7XG4gICAgc2V0SXNEaXJ0eSh0cnVlKTtcbiAgfSwgW25vZGVzLCBzZXROb2Rlc10pO1xuXG4gIGNvbnN0IGhhbmRsZUNvbmZpZ3VyZU5vZGUgPSB1c2VDYWxsYmFjaygobm9kZUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBub2RlID0gbm9kZXMuZmluZChuID0+IG4uaWQgPT09IG5vZGVJZCk7XG4gICAgaWYgKG5vZGUpIHtcbiAgICAgIHNldFNlbGVjdGVkTm9kZShub2RlKTtcbiAgICB9XG4gIH0sIFtub2Rlc10pO1xuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF3b3JrZmxvdyAmJiB3b3JrZmxvd0lkID09PSAnbmV3Jykge1xuICAgICAgLy8gU2hvdyBzYXZlIGRpYWxvZyBmb3IgbmV3IHdvcmtmbG93XG4gICAgICBjb25zdCBuYW1lID0gcHJvbXB0KCdFbnRlciB3b3JrZmxvdyBuYW1lOicpO1xuICAgICAgaWYgKCFuYW1lKSByZXR1cm47XG4gICAgICBcbiAgICAgIC8vIFRPRE86IEltcGxlbWVudCBzYXZlIG5ldyB3b3JrZmxvd1xuICAgICAgY29uc29sZS5sb2coJ1NhdmluZyBuZXcgd29ya2Zsb3c6JywgbmFtZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyB3b3JrZmxvd1xuICAgICAgc2V0SXNTYXZpbmcodHJ1ZSk7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUT0RPOiBJbXBsZW1lbnQgdXBkYXRlIHdvcmtmbG93IEFQSSBjYWxsXG4gICAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyB3b3JrZmxvdzonLCB3b3JrZmxvd0lkKTtcbiAgICAgICAgc2V0SXNEaXJ0eShmYWxzZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSB3b3JrZmxvdzonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc1NhdmluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUV4ZWN1dGUgPSBhc3luYyAoKSA9PiB7XG4gICAgLy8gVE9ETzogSW1wbGVtZW50IHdvcmtmbG93IGV4ZWN1dGlvblxuICAgIGNvbnNvbGUubG9nKCdFeGVjdXRpbmcgd29ya2Zsb3cnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVBZGROb2RlID0gKG5vZGVUeXBlOiBzdHJpbmcsIHBvc2l0aW9uOiB7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0pID0+IHtcbiAgICBsZXQgZGVmYXVsdENvbmZpZyA9IHt9O1xuICAgIGxldCBpc0NvbmZpZ3VyZWQgPSB0cnVlO1xuXG4gICAgLy8gU2V0IHByb3BlciBkZWZhdWx0IGNvbmZpZyBmb3Igc3BlY2lmaWMgbm9kZSB0eXBlc1xuICAgIGlmIChub2RlVHlwZSA9PT0gJ3Byb3ZpZGVyJykge1xuICAgICAgZGVmYXVsdENvbmZpZyA9IHtcbiAgICAgICAgcHJvdmlkZXJJZDogJycsXG4gICAgICAgIG1vZGVsSWQ6ICcnLFxuICAgICAgICBhcGlLZXk6ICcnLFxuICAgICAgICBwYXJhbWV0ZXJzOiB7XG4gICAgICAgICAgdGVtcGVyYXR1cmU6IDEuMCxcbiAgICAgICAgICBtYXhUb2tlbnM6IHVuZGVmaW5lZCxcbiAgICAgICAgICB0b3BQOiB1bmRlZmluZWQsXG4gICAgICAgICAgZnJlcXVlbmN5UGVuYWx0eTogdW5kZWZpbmVkLFxuICAgICAgICAgIHByZXNlbmNlUGVuYWx0eTogdW5kZWZpbmVkLFxuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgaXNDb25maWd1cmVkID0gZmFsc2U7XG4gICAgfSBlbHNlIGlmIChub2RlVHlwZSA9PT0gJ2NlbnRyYWxSb3V0ZXInKSB7XG4gICAgICBkZWZhdWx0Q29uZmlnID0ge1xuICAgICAgICByb3V0aW5nU3RyYXRlZ3k6ICdzbWFydCcsXG4gICAgICAgIGZhbGxiYWNrUHJvdmlkZXI6ICcnLFxuICAgICAgICBtYXhSZXRyaWVzOiAzLFxuICAgICAgICB0aW1lb3V0OiAzMDAwMCxcbiAgICAgICAgZW5hYmxlQ2FjaGluZzogdHJ1ZSxcbiAgICAgICAgZGVidWdNb2RlOiBmYWxzZVxuICAgICAgfTtcbiAgICAgIGlzQ29uZmlndXJlZCA9IHRydWU7XG4gICAgfVxuXG4gICAgY29uc3QgbmV3Tm9kZTogV29ya2Zsb3dOb2RlID0ge1xuICAgICAgaWQ6IGAke25vZGVUeXBlfS0ke0RhdGUubm93KCl9YCxcbiAgICAgIHR5cGU6IG5vZGVUeXBlIGFzIGFueSxcbiAgICAgIHBvc2l0aW9uLFxuICAgICAgZGF0YToge1xuICAgICAgICBsYWJlbDogbm9kZVR5cGUgPT09ICdjZW50cmFsUm91dGVyJyA/ICdDZW50cmFsIFJvdXRlcicgOiBub2RlVHlwZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIG5vZGVUeXBlLnNsaWNlKDEpLFxuICAgICAgICBjb25maWc6IGRlZmF1bHRDb25maWcsXG4gICAgICAgIGlzQ29uZmlndXJlZCxcbiAgICAgICAgZGVzY3JpcHRpb246IGAke25vZGVUeXBlfSBub2RlYFxuICAgICAgfVxuICAgIH07XG5cbiAgICBzZXROb2RlcygobmRzKSA9PiBbLi4ubmRzLCBuZXdOb2RlXSk7XG4gICAgc2V0SXNEaXJ0eSh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVOb2RlVXBkYXRlID0gKG5vZGVJZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFdvcmtmbG93Tm9kZVsnZGF0YSddPikgPT4ge1xuICAgIHNldE5vZGVzKChuZHMpID0+XG4gICAgICBuZHMubWFwKChub2RlKSA9PlxuICAgICAgICBub2RlLmlkID09PSBub2RlSWRcbiAgICAgICAgICA/IHsgLi4ubm9kZSwgZGF0YTogeyAuLi5ub2RlLmRhdGEsIC4uLnVwZGF0ZXMgfSB9XG4gICAgICAgICAgOiBub2RlXG4gICAgICApXG4gICAgKTtcbiAgICBzZXRJc0RpcnR5KHRydWUpO1xuICB9O1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiBiZy1bIzA0MDcxNl0gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+TG9hZGluZyB3b3JrZmxvdy4uLjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiBiZy1bIzA0MDcxNl0gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgey8qIFRvb2xiYXIgKi99XG4gICAgICA8V29ya2Zsb3dUb29sYmFyXG4gICAgICAgIHdvcmtmbG93PXt3b3JrZmxvd31cbiAgICAgICAgaXNEaXJ0eT17aXNEaXJ0eX1cbiAgICAgICAgaXNTYXZpbmc9e2lzU2F2aW5nfVxuICAgICAgICBvblNhdmU9e2hhbmRsZVNhdmV9XG4gICAgICAgIG9uRXhlY3V0ZT17aGFuZGxlRXhlY3V0ZX1cbiAgICAgICAgb25CYWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL21hbnVhbC1idWlsZCcpfVxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleFwiPlxuICAgICAgICB7LyogTm9kZSBQYWxldHRlICovfVxuICAgICAgICA8Tm9kZVBhbGV0dGUgb25BZGROb2RlPXtoYW5kbGVBZGROb2RlfSAvPlxuXG4gICAgICAgIHsvKiBNYWluIENhbnZhcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmUgbWFudWFsLWJ1aWxkLWNhbnZhc1wiPlxuICAgICAgICAgIDxSZWFjdEZsb3dcbiAgICAgICAgICAgIG5vZGVzPXtub2Rlc31cbiAgICAgICAgICAgIGVkZ2VzPXtlZGdlc31cbiAgICAgICAgICAgIG9uTm9kZXNDaGFuZ2U9e29uTm9kZXNDaGFuZ2V9XG4gICAgICAgICAgICBvbkVkZ2VzQ2hhbmdlPXtvbkVkZ2VzQ2hhbmdlfVxuICAgICAgICAgICAgb25Db25uZWN0PXtvbkNvbm5lY3R9XG4gICAgICAgICAgICBvbk5vZGVDbGljaz17b25Ob2RlQ2xpY2t9XG4gICAgICAgICAgICBvbk5vZGVDb250ZXh0TWVudT17b25Ob2RlQ29udGV4dE1lbnV9XG4gICAgICAgICAgICBvbkVkZ2VDb250ZXh0TWVudT17b25FZGdlQ29udGV4dE1lbnV9XG4gICAgICAgICAgICBvblBhbmVDbGljaz17b25QYW5lQ2xpY2t9XG4gICAgICAgICAgICBub2RlVHlwZXM9e25vZGVUeXBlc31cbiAgICAgICAgICAgIGZpdFZpZXdcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLVsjMDQwNzE2XVwiXG4gICAgICAgICAgICBkZWZhdWx0Vmlld3BvcnQ9e3sgeDogMCwgeTogMCwgem9vbTogMC44IH19XG4gICAgICAgICAgICBjb25uZWN0aW9uTGluZVN0eWxlPXt7IHN0cm9rZTogJyNmZjZiMzUnLCBzdHJva2VXaWR0aDogMiB9fVxuICAgICAgICAgICAgZGVmYXVsdEVkZ2VPcHRpb25zPXt7XG4gICAgICAgICAgICAgIHN0eWxlOiB7IHN0cm9rZTogJyNmZjZiMzUnLCBzdHJva2VXaWR0aDogMiB9LFxuICAgICAgICAgICAgICB0eXBlOiAnc21vb3Roc3RlcCcsXG4gICAgICAgICAgICAgIGFuaW1hdGVkOiB0cnVlLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8QmFja2dyb3VuZFxuICAgICAgICAgICAgICBjb2xvcj1cIiMxZjI5MzdcIlxuICAgICAgICAgICAgICBnYXA9ezIwfVxuICAgICAgICAgICAgICBzaXplPXsxfVxuICAgICAgICAgICAgICB2YXJpYW50PVwiZG90c1wiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPENvbnRyb2xzXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzkwIGJvcmRlciBib3JkZXItZ3JheS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICAgICAgICAgIHNob3dJbnRlcmFjdGl2ZT17ZmFsc2V9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPE1pbmlNYXBcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvOTAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtXCJcbiAgICAgICAgICAgICAgbm9kZUNvbG9yPVwiI2ZmNmIzNVwiXG4gICAgICAgICAgICAgIG1hc2tDb2xvcj1cInJnYmEoMCwgMCwgMCwgMC4yKVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvUmVhY3RGbG93PlxuXG4gICAgICAgICAgey8qIENvbnRleHQgTWVudSAqL31cbiAgICAgICAgICB7Y29udGV4dE1lbnUgJiYgKFxuICAgICAgICAgICAgPENvbnRleHRNZW51XG4gICAgICAgICAgICAgIGlkPXtjb250ZXh0TWVudS5pZH1cbiAgICAgICAgICAgICAgdHlwZT17Y29udGV4dE1lbnUudHlwZX1cbiAgICAgICAgICAgICAgbm9kZVR5cGU9e2NvbnRleHRNZW51Lm5vZGVUeXBlfVxuICAgICAgICAgICAgICB0b3A9e2NvbnRleHRNZW51Lnl9XG4gICAgICAgICAgICAgIGxlZnQ9e2NvbnRleHRNZW51Lnh9XG4gICAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldENvbnRleHRNZW51KG51bGwpfVxuICAgICAgICAgICAgICBvbkRlbGV0ZT17Y29udGV4dE1lbnUudHlwZSA9PT0gJ25vZGUnID8gaGFuZGxlRGVsZXRlTm9kZSA6IGhhbmRsZURlbGV0ZUVkZ2V9XG4gICAgICAgICAgICAgIG9uRHVwbGljYXRlPXtjb250ZXh0TWVudS50eXBlID09PSAnbm9kZScgPyBoYW5kbGVEdXBsaWNhdGVOb2RlIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgICBvbkNvbmZpZ3VyZT17Y29udGV4dE1lbnUudHlwZSA9PT0gJ25vZGUnID8gaGFuZGxlQ29uZmlndXJlTm9kZSA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgICAgb25EaXNjb25uZWN0PXtjb250ZXh0TWVudS50eXBlID09PSAnZWRnZScgPyBoYW5kbGVEZWxldGVFZGdlIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29uZmlndXJhdGlvbiBQYW5lbCAqL31cbiAgICAgICAge3NlbGVjdGVkTm9kZSAmJiAoXG4gICAgICAgICAgPE5vZGVDb25maWdQYW5lbFxuICAgICAgICAgICAgbm9kZT17c2VsZWN0ZWROb2RlfVxuICAgICAgICAgICAgb25VcGRhdGU9eyh1cGRhdGVzKSA9PiBoYW5kbGVOb2RlVXBkYXRlKHNlbGVjdGVkTm9kZS5pZCwgdXBkYXRlcyl9XG4gICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTZWxlY3RlZE5vZGUobnVsbCl9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJSZWFjdEZsb3ciLCJCYWNrZ3JvdW5kIiwiQ29udHJvbHMiLCJNaW5pTWFwIiwidXNlTm9kZXNTdGF0ZSIsInVzZUVkZ2VzU3RhdGUiLCJhZGRFZGdlIiwiV29ya2Zsb3dUb29sYmFyIiwiTm9kZVBhbGV0dGUiLCJOb2RlQ29uZmlnUGFuZWwiLCJDb250ZXh0TWVudSIsIm5vZGVUeXBlcyIsIldvcmtmbG93RWRpdG9yUGFnZSIsInBhcmFtcyIsInJlc29sdmVkUGFyYW1zIiwicm91dGVyIiwid29ya2Zsb3dJZCIsIndvcmtmbG93Iiwic2V0V29ya2Zsb3ciLCJub2RlcyIsInNldE5vZGVzIiwib25Ob2Rlc0NoYW5nZSIsImVkZ2VzIiwic2V0RWRnZXMiLCJvbkVkZ2VzQ2hhbmdlIiwic2VsZWN0ZWROb2RlIiwic2V0U2VsZWN0ZWROb2RlIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNTYXZpbmciLCJzZXRJc1NhdmluZyIsImlzRGlydHkiLCJzZXRJc0RpcnR5IiwiY29udGV4dE1lbnUiLCJzZXRDb250ZXh0TWVudSIsImluaXRpYWxpemVOZXdXb3JrZmxvdyIsImxvYWRXb3JrZmxvdyIsImRlZmF1bHROb2RlcyIsImlkIiwidHlwZSIsInBvc2l0aW9uIiwieCIsInkiLCJkYXRhIiwibGFiZWwiLCJjb25maWciLCJpc0NvbmZpZ3VyZWQiLCJkZXNjcmlwdGlvbiIsImRlZmF1bHRFZGdlcyIsInNvdXJjZSIsInRhcmdldCIsImFuaW1hdGVkIiwiZXJyb3IiLCJjb25zb2xlIiwibG9nIiwib25Db25uZWN0IiwibmV3RWRnZSIsImxlbmd0aCIsImVkcyIsIm9uTm9kZUNsaWNrIiwiZXZlbnQiLCJub2RlIiwib25QYW5lQ2xpY2siLCJvbk5vZGVDb250ZXh0TWVudSIsInByZXZlbnREZWZhdWx0Iiwibm9kZVR5cGUiLCJjbGllbnRYIiwiY2xpZW50WSIsIm9uRWRnZUNvbnRleHRNZW51IiwiZWRnZSIsImhhbmRsZURlbGV0ZU5vZGUiLCJub2RlSWQiLCJjb3JlTm9kZXMiLCJpbmNsdWRlcyIsIm5kcyIsImZpbHRlciIsImhhbmRsZURlbGV0ZUVkZ2UiLCJlZGdlSWQiLCJoYW5kbGVEdXBsaWNhdGVOb2RlIiwibm9kZVRvRHVwbGljYXRlIiwiZmluZCIsIm4iLCJuZXdOb2RlIiwiRGF0ZSIsIm5vdyIsImhhbmRsZUNvbmZpZ3VyZU5vZGUiLCJoYW5kbGVTYXZlIiwibmFtZSIsInByb21wdCIsImhhbmRsZUV4ZWN1dGUiLCJoYW5kbGVBZGROb2RlIiwiZGVmYXVsdENvbmZpZyIsInByb3ZpZGVySWQiLCJtb2RlbElkIiwiYXBpS2V5IiwicGFyYW1ldGVycyIsInRlbXBlcmF0dXJlIiwibWF4VG9rZW5zIiwidW5kZWZpbmVkIiwidG9wUCIsImZyZXF1ZW5jeVBlbmFsdHkiLCJwcmVzZW5jZVBlbmFsdHkiLCJyb3V0aW5nU3RyYXRlZ3kiLCJmYWxsYmFja1Byb3ZpZGVyIiwibWF4UmV0cmllcyIsInRpbWVvdXQiLCJlbmFibGVDYWNoaW5nIiwiZGVidWdNb2RlIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsImhhbmRsZU5vZGVVcGRhdGUiLCJ1cGRhdGVzIiwibWFwIiwiZGl2IiwiY2xhc3NOYW1lIiwib25TYXZlIiwib25FeGVjdXRlIiwib25CYWNrIiwicHVzaCIsIm9uQWRkTm9kZSIsImZpdFZpZXciLCJkZWZhdWx0Vmlld3BvcnQiLCJ6b29tIiwiY29ubmVjdGlvbkxpbmVTdHlsZSIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwiZGVmYXVsdEVkZ2VPcHRpb25zIiwic3R5bGUiLCJjb2xvciIsImdhcCIsInNpemUiLCJ2YXJpYW50Iiwic2hvd0ludGVyYWN0aXZlIiwibm9kZUNvbG9yIiwibWFza0NvbG9yIiwidG9wIiwibGVmdCIsIm9uQ2xvc2UiLCJvbkRlbGV0ZSIsIm9uRHVwbGljYXRlIiwib25Db25maWd1cmUiLCJvbkRpc2Nvbm5lY3QiLCJvblVwZGF0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ })

});