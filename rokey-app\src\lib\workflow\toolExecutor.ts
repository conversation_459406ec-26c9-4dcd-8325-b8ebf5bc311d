// Workflow Tool Executor
// This handles execution of tools within Manual Build workflows

import { WebBrowsingTool } from '@/lib/tools/webBrowsing';
import { ToolNodeData } from '@/types/manualBuild';

export interface ToolExecutionContext {
  userInput: string;
  previousResults?: any[];
  workflowId: string;
  nodeId: string;
}

export interface ToolExecutionResult {
  success: boolean;
  data: any;
  toolType: string;
  executionTime: number;
  error?: string;
}

export class ToolExecutor {
  static async executeToolNode(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      let result;
      
      switch (nodeConfig.toolType) {
        case 'web_browsing':
          result = await this.executeWebBrowsing(nodeConfig, context);
          break;
          
        case 'google_drive':
        case 'google_docs':
        case 'google_sheets':
        case 'zapier':
        case 'notion':
        case 'calendar':
        case 'gmail':
        case 'youtube':
        case 'supabase':
          throw new Error(`${nodeConfig.toolType} integration is not yet implemented`);
          
        default:
          throw new Error(`Unknown tool type: ${nodeConfig.toolType}`);
      }

      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result.data,
        toolType: nodeConfig.toolType,
        executionTime,
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        data: null,
        toolType: nodeConfig.toolType || 'unknown',
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private static async executeWebBrowsing(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ) {
    const { userInput } = context;
    
    // Use the WebBrowsingTool utility to execute based on node configuration
    return await WebBrowsingTool.executeFromNodeConfig(nodeConfig, userInput);
  }

  // Helper method to validate tool configuration
  static validateToolConfig(nodeConfig: ToolNodeData['config']): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeConfig.toolType) {
      errors.push('Tool type is required');
    }

    switch (nodeConfig.toolType) {
      case 'web_browsing':
        // Web browsing is always valid as it doesn't require authentication
        if (!nodeConfig.extractionType) {
          errors.push('Extraction type is required for web browsing');
        }
        if (nodeConfig.extractionType === 'custom' && !nodeConfig.customSelector) {
          errors.push('Custom selector is required when using custom extraction type');
        }
        break;
        
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'zapier':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        if (!nodeConfig.isAuthenticated) {
          errors.push(`${nodeConfig.toolType} requires authentication`);
        }
        break;
        
      default:
        errors.push(`Unknown tool type: ${nodeConfig.toolType}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get human-readable description of what the tool will do
  static getToolDescription(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return WebBrowsingTool.getActionDescription(nodeConfig);
      case 'google_drive':
        return 'Access and manage Google Drive files';
      case 'google_docs':
        return 'Create and edit Google Documents';
      case 'google_sheets':
        return 'Work with Google Spreadsheets';
      case 'zapier':
        return 'Connect with 5000+ apps via Zapier';
      case 'notion':
        return 'Access Notion databases and pages';
      case 'calendar':
        return 'Manage calendar events and schedules';
      case 'gmail':
        return 'Send and manage emails';
      case 'youtube':
        return 'Access YouTube data and analytics';
      case 'supabase':
        return 'Direct database operations';
      default:
        return 'External tool integration';
    }
  }

  // Get example usage for the tool
  static getToolExampleUsage(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return WebBrowsingTool.getExampleUsage(nodeConfig.extractionType || 'content');
      case 'google_drive':
        return 'Example: "list files in folder" or "upload document"';
      case 'google_docs':
        return 'Example: "create new document" or "edit document content"';
      case 'google_sheets':
        return 'Example: "add row to spreadsheet" or "calculate sum"';
      case 'zapier':
        return 'Example: "trigger workflow" or "send data to app"';
      case 'notion':
        return 'Example: "create page" or "query database"';
      case 'calendar':
        return 'Example: "schedule meeting" or "check availability"';
      case 'gmail':
        return 'Example: "send email" or "check inbox"';
      case 'youtube':
        return 'Example: "get video stats" or "upload video"';
      case 'supabase':
        return 'Example: "query table" or "insert record"';
      default:
        return 'Tool-specific input required';
    }
  }

  // Check if tool is ready to use
  static isToolReady(nodeConfig: ToolNodeData['config']): boolean {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return true; // Web browsing is always ready
      default:
        return nodeConfig.isAuthenticated || false;
    }
  }

  // Get tool status
  static getToolStatus(nodeConfig: ToolNodeData['config']): {
    status: 'ready' | 'needs_auth' | 'error' | 'not_implemented';
    message: string;
  } {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return {
          status: 'ready',
          message: 'Web browsing is ready to use'
        };
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'zapier':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        return {
          status: 'not_implemented',
          message: `${nodeConfig.toolType} integration coming soon`
        };
      default:
        return {
          status: 'error',
          message: 'Unknown tool type'
        };
    }
  }
}
