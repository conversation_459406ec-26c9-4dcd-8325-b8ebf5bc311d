'use client';

import { WrenchScrewdriverIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, ToolNodeData } from '@/types/manualBuild';

const toolIcons = {
  web_browsing: '🌐',
  google_drive: '📁',
  google_docs: '📄',
  zapier: '⚡',
  notion: '📝',
  google_sheets: '📊',
  calendar: '📅',
  gmail: '📧',
  youtube: '📺',
  supabase: '🗄️'
};

const toolNames = {
  web_browsing: 'Web Browsing',
  google_drive: 'Google Drive',
  google_docs: 'Google Docs',
  zapier: 'Zapier',
  notion: 'Notion',
  google_sheets: 'Google Sheets',
  calendar: 'Calendar',
  gmail: 'Gmail',
  youtube: 'YouTube',
  supabase: 'Supabase'
};

export default function ToolNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config as ToolNodeData['config'];
  const toolType = config?.toolType;
  const toolIcon = toolType ? toolIcons[toolType] : '🔧';
  const toolName = toolType ? toolNames[toolType] : 'External Tool';
  const isWebBrowsing = toolType === 'web_browsing';
  const connectionStatus = config?.connectionStatus || 'disconnected';

  const getStatusColor = () => {
    if (isWebBrowsing) return 'text-green-400'; // Web browsing is always available
    switch (connectionStatus) {
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getStatusText = () => {
    if (isWebBrowsing) return 'Ready';
    switch (connectionStatus) {
      case 'connected': return 'Connected';
      case 'error': return 'Error';
      default: return 'Not Connected';
    }
  };

  return (
    <BaseNode
      data={data}
      icon={WrenchScrewdriverIcon}
      color="#06b6d4"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-3">
        {toolType ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">{toolIcon}</span>
                <span className="text-sm font-medium text-white">
                  {toolName}
                </span>
              </div>
              <div className={`text-xs ${getStatusColor()}`}>
                ●
              </div>
            </div>

            <div className={`text-xs ${getStatusColor()}`}>
              {getStatusText()}
            </div>

            {isWebBrowsing && config?.searchEngine && (
              <div className="text-xs text-gray-400">
                Engine: {config.searchEngine}
              </div>
            )}

            {config?.timeout && (
              <div className="text-xs text-gray-400">
                Timeout: {config.timeout}s
              </div>
            )}
            
            <div className="text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded">
              ✓ Tool configured
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              External Tool Integration
            </div>
            <div className="text-xs text-gray-400">
              Connect to external services like Google Drive, databases, APIs, or browser automation.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
