"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/SwitchNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/SwitchNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SwitchNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SwitchNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const switchType = config === null || config === void 0 ? void 0 : config.switchType;\n    const cases = (config === null || config === void 0 ? void 0 : config.cases) || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                className: \"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    left: -12\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute text-xs text-gray-300 font-medium pointer-events-none\",\n                style: {\n                    left: -50,\n                    top: '45%'\n                },\n                children: \"Input\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 border-indigo-500 bg-indigo-900/20 backdrop-blur-sm shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-indigo-500/20 to-indigo-600/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg bg-indigo-500/20 text-indigo-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Route to different paths\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-indigo-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50 space-y-3\",\n                        children: switchType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: [\n                                        \"Type: \",\n                                        switchType\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                cases.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Cases:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 19\n                                        }, this),\n                                        cases.slice(0, 3).map((caseItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs bg-indigo-900/30 text-indigo-300 px-2 py-0.5 rounded\",\n                                                children: caseItem.label || \"Case \".concat(index + 1)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 21\n                                            }, this)),\n                                        cases.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"+\",\n                                                cases.length - 3,\n                                                \" more cases\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"Switch Router\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Route workflow to different paths based on input values or conditions.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                                    children: \"⚠️ Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            cases.length > 0 ? cases.slice(0, 4).map((caseItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                    type: \"source\",\n                    position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                    id: \"case-\".concat(index),\n                    className: \"w-4 h-4 border-2 border-indigo-500 bg-indigo-600 hover:border-indigo-400 hover:bg-indigo-500 transition-colors\",\n                    style: {\n                        right: -8,\n                        top: \"\".concat(30 + index * 15, \"%\")\n                    }\n                }, index, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    right: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\SwitchNode.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = SwitchNode;\nvar _c;\n$RefreshReg$(_c, \"SwitchNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/SwitchNode.tsx\n"));

/***/ })

});