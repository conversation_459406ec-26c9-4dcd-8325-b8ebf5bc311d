"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/index.ts":
/*!****************************************************!*\
  !*** ./src/components/manual-build/nodes/index.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingNode: () => (/* reexport safe */ _BrowsingNode__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   CentralRouterNode: () => (/* reexport safe */ _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   ClassifierNode: () => (/* reexport safe */ _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ConditionalNode: () => (/* reexport safe */ _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   LoopNode: () => (/* reexport safe */ _LoopNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   MemoryNode: () => (/* reexport safe */ _MemoryNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   MergeNode: () => (/* reexport safe */ _MergeNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   OutputNode: () => (/* reexport safe */ _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   PlannerNode: () => (/* reexport safe */ _PlannerNode__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   ProviderNode: () => (/* reexport safe */ _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   RoleAgentNode: () => (/* reexport safe */ _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   SwitchNode: () => (/* reexport safe */ _SwitchNode__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   ToolNode: () => (/* reexport safe */ _ToolNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   UserRequestNode: () => (/* reexport safe */ _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   VisionNode: () => (/* reexport safe */ _VisionNode__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   nodeTypes: () => (/* binding */ nodeTypes)\n/* harmony export */ });\n/* harmony import */ var _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserRequestNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/UserRequestNode.tsx\");\n/* harmony import */ var _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClassifierNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ClassifierNode.tsx\");\n/* harmony import */ var _ProviderNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProviderNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\");\n/* harmony import */ var _VisionNode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VisionNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\");\n/* harmony import */ var _OutputNode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OutputNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/OutputNode.tsx\");\n/* harmony import */ var _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RoleAgentNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/RoleAgentNode.tsx\");\n/* harmony import */ var _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CentralRouterNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx\");\n/* harmony import */ var _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConditionalNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ConditionalNode.tsx\");\n/* harmony import */ var _MergeNode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MergeNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MergeNode.tsx\");\n/* harmony import */ var _LoopNode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LoopNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/LoopNode.tsx\");\n/* harmony import */ var _ToolNode__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ToolNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\");\n/* harmony import */ var _MemoryNode__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MemoryNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\");\n/* harmony import */ var _SwitchNode__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./SwitchNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/SwitchNode.tsx\");\n/* harmony import */ var _PlannerNode__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PlannerNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx\");\n/* harmony import */ var _BrowsingNode__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./BrowsingNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\");\n// Node Types Registry for React Flow\n// This file exports all custom node components for the Manual Build workflow editor\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Export all node types for React Flow\nconst nodeTypes = {\n    userRequest: _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    classifier: _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    provider: _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    vision: _VisionNode__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    output: _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    roleAgent: _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    centralRouter: _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    conditional: _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    merge: _MergeNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    loop: _LoopNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    tool: _ToolNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    memory: _MemoryNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    switch: _SwitchNode__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    planner: _PlannerNode__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    browsing: _BrowsingNode__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n// Export individual components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEscUNBQXFDO0FBQ3JDLG9GQUFvRjtBQUVwQztBQUNGO0FBQ0o7QUFDSjtBQUNBO0FBQ007QUFDUTtBQUNKO0FBQ1o7QUFDRjtBQUNBO0FBQ0k7QUFDQTtBQUNFO0FBQ0U7QUFFMUMsdUNBQXVDO0FBQ2hDLE1BQU1lLFlBQVk7SUFDdkJDLGFBQWFoQix3REFBZUE7SUFDNUJpQixZQUFZaEIsdURBQWNBO0lBQzFCaUIsVUFBVWhCLHFEQUFZQTtJQUN0QmlCLFFBQVFoQixtREFBVUE7SUFDbEJpQixRQUFRaEIsbURBQVVBO0lBQ2xCaUIsV0FBV2hCLHNEQUFhQTtJQUN4QmlCLGVBQWVoQiwwREFBaUJBO0lBQ2hDaUIsYUFBYWhCLHdEQUFlQTtJQUM1QmlCLE9BQU9oQixrREFBU0E7SUFDaEJpQixNQUFNaEIsaURBQVFBO0lBQ2RpQixNQUFNaEIsa0RBQVFBO0lBQ2RpQixRQUFRaEIsb0RBQVVBO0lBQ2xCaUIsUUFBUWhCLG9EQUFVQTtJQUNsQmlCLFNBQVNoQixxREFBV0E7SUFDcEJpQixVQUFVaEIsc0RBQVlBO0FBQ3hCLEVBQUU7QUFFRiwrQkFBK0I7QUFpQjdCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcbWFudWFsLWJ1aWxkXFxub2Rlc1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTm9kZSBUeXBlcyBSZWdpc3RyeSBmb3IgUmVhY3QgRmxvd1xuLy8gVGhpcyBmaWxlIGV4cG9ydHMgYWxsIGN1c3RvbSBub2RlIGNvbXBvbmVudHMgZm9yIHRoZSBNYW51YWwgQnVpbGQgd29ya2Zsb3cgZWRpdG9yXG5cbmltcG9ydCBVc2VyUmVxdWVzdE5vZGUgZnJvbSAnLi9Vc2VyUmVxdWVzdE5vZGUnO1xuaW1wb3J0IENsYXNzaWZpZXJOb2RlIGZyb20gJy4vQ2xhc3NpZmllck5vZGUnO1xuaW1wb3J0IFByb3ZpZGVyTm9kZSBmcm9tICcuL1Byb3ZpZGVyTm9kZSc7XG5pbXBvcnQgVmlzaW9uTm9kZSBmcm9tICcuL1Zpc2lvbk5vZGUnO1xuaW1wb3J0IE91dHB1dE5vZGUgZnJvbSAnLi9PdXRwdXROb2RlJztcbmltcG9ydCBSb2xlQWdlbnROb2RlIGZyb20gJy4vUm9sZUFnZW50Tm9kZSc7XG5pbXBvcnQgQ2VudHJhbFJvdXRlck5vZGUgZnJvbSAnLi9DZW50cmFsUm91dGVyTm9kZSc7XG5pbXBvcnQgQ29uZGl0aW9uYWxOb2RlIGZyb20gJy4vQ29uZGl0aW9uYWxOb2RlJztcbmltcG9ydCBNZXJnZU5vZGUgZnJvbSAnLi9NZXJnZU5vZGUnO1xuaW1wb3J0IExvb3BOb2RlIGZyb20gJy4vTG9vcE5vZGUnO1xuaW1wb3J0IFRvb2xOb2RlIGZyb20gJy4vVG9vbE5vZGUnO1xuaW1wb3J0IE1lbW9yeU5vZGUgZnJvbSAnLi9NZW1vcnlOb2RlJztcbmltcG9ydCBTd2l0Y2hOb2RlIGZyb20gJy4vU3dpdGNoTm9kZSc7XG5pbXBvcnQgUGxhbm5lck5vZGUgZnJvbSAnLi9QbGFubmVyTm9kZSc7XG5pbXBvcnQgQnJvd3NpbmdOb2RlIGZyb20gJy4vQnJvd3NpbmdOb2RlJztcblxuLy8gRXhwb3J0IGFsbCBub2RlIHR5cGVzIGZvciBSZWFjdCBGbG93XG5leHBvcnQgY29uc3Qgbm9kZVR5cGVzID0ge1xuICB1c2VyUmVxdWVzdDogVXNlclJlcXVlc3ROb2RlLFxuICBjbGFzc2lmaWVyOiBDbGFzc2lmaWVyTm9kZSxcbiAgcHJvdmlkZXI6IFByb3ZpZGVyTm9kZSxcbiAgdmlzaW9uOiBWaXNpb25Ob2RlLFxuICBvdXRwdXQ6IE91dHB1dE5vZGUsXG4gIHJvbGVBZ2VudDogUm9sZUFnZW50Tm9kZSxcbiAgY2VudHJhbFJvdXRlcjogQ2VudHJhbFJvdXRlck5vZGUsXG4gIGNvbmRpdGlvbmFsOiBDb25kaXRpb25hbE5vZGUsXG4gIG1lcmdlOiBNZXJnZU5vZGUsXG4gIGxvb3A6IExvb3BOb2RlLFxuICB0b29sOiBUb29sTm9kZSxcbiAgbWVtb3J5OiBNZW1vcnlOb2RlLFxuICBzd2l0Y2g6IFN3aXRjaE5vZGUsXG4gIHBsYW5uZXI6IFBsYW5uZXJOb2RlLFxuICBicm93c2luZzogQnJvd3NpbmdOb2RlLFxufTtcblxuLy8gRXhwb3J0IGluZGl2aWR1YWwgY29tcG9uZW50c1xuZXhwb3J0IHtcbiAgVXNlclJlcXVlc3ROb2RlLFxuICBDbGFzc2lmaWVyTm9kZSxcbiAgUHJvdmlkZXJOb2RlLFxuICBWaXNpb25Ob2RlLFxuICBPdXRwdXROb2RlLFxuICBSb2xlQWdlbnROb2RlLFxuICBDZW50cmFsUm91dGVyTm9kZSxcbiAgQ29uZGl0aW9uYWxOb2RlLFxuICBNZXJnZU5vZGUsXG4gIExvb3BOb2RlLFxuICBUb29sTm9kZSxcbiAgTWVtb3J5Tm9kZSxcbiAgU3dpdGNoTm9kZSxcbiAgUGxhbm5lck5vZGUsXG4gIEJyb3dzaW5nTm9kZSxcbn07XG4iXSwibmFtZXMiOlsiVXNlclJlcXVlc3ROb2RlIiwiQ2xhc3NpZmllck5vZGUiLCJQcm92aWRlck5vZGUiLCJWaXNpb25Ob2RlIiwiT3V0cHV0Tm9kZSIsIlJvbGVBZ2VudE5vZGUiLCJDZW50cmFsUm91dGVyTm9kZSIsIkNvbmRpdGlvbmFsTm9kZSIsIk1lcmdlTm9kZSIsIkxvb3BOb2RlIiwiVG9vbE5vZGUiLCJNZW1vcnlOb2RlIiwiU3dpdGNoTm9kZSIsIlBsYW5uZXJOb2RlIiwiQnJvd3NpbmdOb2RlIiwibm9kZVR5cGVzIiwidXNlclJlcXVlc3QiLCJjbGFzc2lmaWVyIiwicHJvdmlkZXIiLCJ2aXNpb24iLCJvdXRwdXQiLCJyb2xlQWdlbnQiLCJjZW50cmFsUm91dGVyIiwiY29uZGl0aW9uYWwiLCJtZXJnZSIsImxvb3AiLCJ0b29sIiwibWVtb3J5Iiwic3dpdGNoIiwicGxhbm5lciIsImJyb3dzaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/index.ts\n"));

/***/ })

});