"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, hasToolsInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', toolsInputLabel = 'Tools', className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-gray-800/95 border border-gray-600 rounded text-xs text-gray-300 font-medium shadow-lg\",\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute transform -translate-y-1/2 flex items-center z-10\",\n                style: {\n                    left: -60,\n                    top: '30%'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-4 h-4 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: 52\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 py-1 bg-purple-900/95 border border-purple-600 rounded text-xs text-purple-200 font-medium shadow-lg\",\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this),\n            hasToolsInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute transform -translate-y-1/2 flex items-center z-10\",\n                style: {\n                    left: -60,\n                    top: '70%'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"tools\",\n                        className: \"w-4 h-4 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                        style: {\n                            left: 52\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 py-1 bg-green-900/95 border border-green-600 rounded text-xs text-green-200 font-medium shadow-lg\",\n                        children: toolsInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    (hasInput || hasRoleInput) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -left-16 top-0 h-full flex flex-col justify-center space-y-2 pointer-events-none\",\n                        children: [\n                            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-xs text-purple-300 bg-purple-900/90 border border-purple-700/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Role\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-0.5 bg-purple-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this),\n                            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-xs text-gray-300 bg-gray-800/90 border border-gray-600/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-0.5 bg-gray-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    right: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});