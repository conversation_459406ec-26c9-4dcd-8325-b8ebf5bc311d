"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction GlobeAltIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n    }));\n}\n_c = GlobeAltIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(GlobeAltIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlobeAltIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0dsb2JlQWx0SWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjtBQUMvQixTQUFTQyxhQUFhLEtBSXJCLEVBQUVDLE1BQU07UUFKYSxFQUNwQkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUpxQjtJQUtwQixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsaUVBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXEdsb2JlQWx0SWNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIEdsb2JlQWx0SWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTEyIDIxYTkuMDA0IDkuMDA0IDAgMCAwIDguNzE2LTYuNzQ3TTEyIDIxYTkuMDA0IDkuMDA0IDAgMCAxLTguNzE2LTYuNzQ3TTEyIDIxYzIuNDg1IDAgNC41LTQuMDMgNC41LTlTMTQuNDg1IDMgMTIgM20wIDE4Yy0yLjQ4NSAwLTQuNS00LjAzLTQuNS05UzkuNTE1IDMgMTIgM20wIDBhOC45OTcgOC45OTcgMCAwIDEgNy44NDMgNC41ODJNMTIgM2E4Ljk5NyA4Ljk5NyAwIDAgMC03Ljg0MyA0LjU4Mm0xNS42ODYgMEExMS45NTMgMTEuOTUzIDAgMCAxIDEyIDEwLjVjLTIuOTk4IDAtNS43NC0xLjEtNy44NDMtMi45MThtMTUuNjg2IDBBOC45NTkgOC45NTkgMCAwIDEgMjEgMTJjMCAuNzc4LS4wOTkgMS41MzMtLjI4NCAyLjI1M20wIDBBMTcuOTE5IDE3LjkxOSAwIDAgMSAxMiAxNi41Yy0zLjE2MiAwLTYuMTMzLS44MTUtOC43MTYtMi4yNDdtMCAwQTkuMDE1IDkuMDE1IDAgMCAxIDMgMTJjMC0xLjYwNS40Mi0zLjExMyAxLjE1Ny00LjQxOFwiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoR2xvYmVBbHRJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbIlJlYWN0IiwiR2xvYmVBbHRJY29uIiwic3ZnUmVmIiwidGl0bGUiLCJ0aXRsZUlkIiwicHJvcHMiLCJjcmVhdGVFbGVtZW50IiwiT2JqZWN0IiwiYXNzaWduIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZVdpZHRoIiwic3Ryb2tlIiwicmVmIiwiaWQiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJkIiwiRm9yd2FyZFJlZiIsImZvcndhcmRSZWYiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BrowsingNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const maxSites = (config === null || config === void 0 ? void 0 : config.maxSites) || 5;\n    const timeout = (config === null || config === void 0 ? void 0 : config.timeout) || 30;\n    var _config_enableScreenshots;\n    const enableScreenshots = (_config_enableScreenshots = config === null || config === void 0 ? void 0 : config.enableScreenshots) !== null && _config_enableScreenshots !== void 0 ? _config_enableScreenshots : true;\n    var _config_enableFormFilling;\n    const enableFormFilling = (_config_enableFormFilling = config === null || config === void 0 ? void 0 : config.enableFormFilling) !== null && _config_enableFormFilling !== void 0 ? _config_enableFormFilling : true;\n    const searchEngines = (config === null || config === void 0 ? void 0 : config.searchEngines) || [\n        'google'\n    ];\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (enableScreenshots) capabilities.push('📸 Screenshots');\n        if (enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#10b981\",\n        hasInput: true,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'planner',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Intelligent Browsing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400\",\n                                    children: \"● Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max sites: \",\n                                maxSites,\n                                \" | Timeout: \",\n                                timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Engines: \",\n                                searchEngines.join(', ')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: getCapabilities().join(' • ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max depth: \",\n                                config.maxDepth,\n                                \" levels\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83C\\uDF10 Autonomous Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Requires: Planner + Memory inputs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx":
/*!***********************************************************!*\
  !*** ./src/components/manual-build/nodes/PlannerNode.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlannerNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PlannerNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const maxSubtasks = (config === null || config === void 0 ? void 0 : config.maxSubtasks) || 10;\n    const getProviderName = (id)=>{\n        const providerMap = {\n            'openai': 'OpenAI',\n            'anthropic': 'Anthropic',\n            'google': 'Google',\n            'groq': 'Groq',\n            'deepseek': 'DeepSeek',\n            'openrouter': 'OpenRouter'\n        };\n        return providerMap[id] || id;\n    };\n    const getModelDisplayName = (modelId)=>{\n        if (!modelId) return '';\n        // Extract model name from ID (e.g., 'gpt-4' from 'openai/gpt-4')\n        const parts = modelId.split('/');\n        return parts[parts.length - 1];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#8b5cf6\",\n        hasInput: false,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                providerId && modelId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: getProviderName(providerId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: getModelDisplayName(modelId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max subtasks: \",\n                                maxSubtasks\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.temperature) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Temperature: \",\n                                config.temperature\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 15\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.planningPrompt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: \"Custom prompt configured\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-400\",\n                    children: \"Configure AI model for planning\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83D\\uDCCB Planning Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = PlannerNode;\nvar _c;\n$RefreshReg$(_c, \"PlannerNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9QbGFubmVyTm9kZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMEQ7QUFFeEI7QUFHbkIsU0FBU0UsWUFBWSxLQUF5QztRQUF6QyxFQUFFQyxJQUFJLEVBQW1DLEdBQXpDO0lBQ2xDLE1BQU1DLFNBQVNELEtBQUtDLE1BQU07SUFDMUIsTUFBTUMsYUFBYUQsbUJBQUFBLDZCQUFBQSxPQUFRQyxVQUFVO0lBQ3JDLE1BQU1DLFVBQVVGLG1CQUFBQSw2QkFBQUEsT0FBUUUsT0FBTztJQUMvQixNQUFNQyxjQUFjSCxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFHLFdBQVcsS0FBSTtJQUUzQyxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsY0FBc0M7WUFDMUMsVUFBVTtZQUNWLGFBQWE7WUFDYixVQUFVO1lBQ1YsUUFBUTtZQUNSLFlBQVk7WUFDWixjQUFjO1FBQ2hCO1FBQ0EsT0FBT0EsV0FBVyxDQUFDRCxHQUFHLElBQUlBO0lBQzVCO0lBRUEsTUFBTUUsc0JBQXNCLENBQUNMO1FBQzNCLElBQUksQ0FBQ0EsU0FBUyxPQUFPO1FBQ3JCLGlFQUFpRTtRQUNqRSxNQUFNTSxRQUFRTixRQUFRTyxLQUFLLENBQUM7UUFDNUIsT0FBT0QsS0FBSyxDQUFDQSxNQUFNRSxNQUFNLEdBQUcsRUFBRTtJQUNoQztJQUVBLHFCQUNFLDhEQUFDYixpREFBUUE7UUFDUEUsTUFBTUE7UUFDTlksTUFBTWYscUdBQVdBO1FBQ2pCZ0IsT0FBTTtRQUNOQyxVQUFVO1FBQ1ZDLFdBQVc7a0JBRVgsNEVBQUNDO1lBQUlDLFdBQVU7O2dCQUNaZixjQUFjQyx3QkFDYiw4REFBQ2E7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFLRCxXQUFVOzhDQUNiWixnQkFBZ0JIOzs7Ozs7OENBRW5CLDhEQUFDZ0I7b0NBQUtELFdBQVU7OENBQ2JULG9CQUFvQkw7Ozs7Ozs7Ozs7OztzQ0FJekIsOERBQUNhOzRCQUFJQyxXQUFVOztnQ0FBd0I7Z0NBQ3RCYjs7Ozs7Ozt3QkFHaEJILENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWtCLFdBQVcsTUFBS0MsMkJBQ3ZCLDhEQUFDSjs0QkFBSUMsV0FBVTs7Z0NBQXdCO2dDQUN2QmhCLE9BQU9rQixXQUFXOzs7Ozs7O3dCQUluQ2xCLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUW9CLGNBQWMsbUJBQ3JCLDhEQUFDTDs0QkFBSUMsV0FBVTtzQ0FBaUM7Ozs7Ozs7Ozs7O3lDQU1wRCw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQXdCOzs7Ozs7OEJBS3pDLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFBNkQ7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBGO0tBekV3QmxCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcbWFudWFsLWJ1aWxkXFxub2Rlc1xcUGxhbm5lck5vZGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgQ3B1Q2hpcEljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgTm9kZVByb3BzIH0gZnJvbSAnQHh5Zmxvdy9yZWFjdCc7XG5pbXBvcnQgQmFzZU5vZGUgZnJvbSAnLi9CYXNlTm9kZSc7XG5pbXBvcnQgeyBXb3JrZmxvd05vZGUsIFBsYW5uZXJOb2RlRGF0YSB9IGZyb20gJ0AvdHlwZXMvbWFudWFsQnVpbGQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQbGFubmVyTm9kZSh7IGRhdGEgfTogTm9kZVByb3BzPFdvcmtmbG93Tm9kZVsnZGF0YSddPikge1xuICBjb25zdCBjb25maWcgPSBkYXRhLmNvbmZpZyBhcyBQbGFubmVyTm9kZURhdGFbJ2NvbmZpZyddO1xuICBjb25zdCBwcm92aWRlcklkID0gY29uZmlnPy5wcm92aWRlcklkO1xuICBjb25zdCBtb2RlbElkID0gY29uZmlnPy5tb2RlbElkO1xuICBjb25zdCBtYXhTdWJ0YXNrcyA9IGNvbmZpZz8ubWF4U3VidGFza3MgfHwgMTA7XG5cbiAgY29uc3QgZ2V0UHJvdmlkZXJOYW1lID0gKGlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBwcm92aWRlck1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgICdvcGVuYWknOiAnT3BlbkFJJyxcbiAgICAgICdhbnRocm9waWMnOiAnQW50aHJvcGljJyxcbiAgICAgICdnb29nbGUnOiAnR29vZ2xlJyxcbiAgICAgICdncm9xJzogJ0dyb3EnLFxuICAgICAgJ2RlZXBzZWVrJzogJ0RlZXBTZWVrJyxcbiAgICAgICdvcGVucm91dGVyJzogJ09wZW5Sb3V0ZXInXG4gICAgfTtcbiAgICByZXR1cm4gcHJvdmlkZXJNYXBbaWRdIHx8IGlkO1xuICB9O1xuXG4gIGNvbnN0IGdldE1vZGVsRGlzcGxheU5hbWUgPSAobW9kZWxJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFtb2RlbElkKSByZXR1cm4gJyc7XG4gICAgLy8gRXh0cmFjdCBtb2RlbCBuYW1lIGZyb20gSUQgKGUuZy4sICdncHQtNCcgZnJvbSAnb3BlbmFpL2dwdC00JylcbiAgICBjb25zdCBwYXJ0cyA9IG1vZGVsSWQuc3BsaXQoJy8nKTtcbiAgICByZXR1cm4gcGFydHNbcGFydHMubGVuZ3RoIC0gMV07XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8QmFzZU5vZGVcbiAgICAgIGRhdGE9e2RhdGF9XG4gICAgICBpY29uPXtDcHVDaGlwSWNvbn1cbiAgICAgIGNvbG9yPVwiIzhiNWNmNlwiXG4gICAgICBoYXNJbnB1dD17ZmFsc2V9IC8vIFBsYW5uZXIgaGFzIG5vIGlucHV0IC0gaXQncyBhIHN0YXJ0aW5nIG5vZGVcbiAgICAgIGhhc091dHB1dD17dHJ1ZX1cbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICB7cHJvdmlkZXJJZCAmJiBtb2RlbElkID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIHtnZXRQcm92aWRlck5hbWUocHJvdmlkZXJJZCl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAge2dldE1vZGVsRGlzcGxheU5hbWUobW9kZWxJZCl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICBNYXggc3VidGFza3M6IHttYXhTdWJ0YXNrc31cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Y29uZmlnPy50ZW1wZXJhdHVyZSAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICBUZW1wZXJhdHVyZToge2NvbmZpZy50ZW1wZXJhdHVyZX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7Y29uZmlnPy5wbGFubmluZ1Byb21wdCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgQ3VzdG9tIHByb21wdCBjb25maWd1cmVkXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgQ29uZmlndXJlIEFJIG1vZGVsIGZvciBwbGFubmluZ1xuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXB1cnBsZS0zMDAgYmctcHVycGxlLTkwMC8zMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgIPCfk4sgUGxhbm5pbmcgQWdlbnRcbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L0Jhc2VOb2RlPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkNwdUNoaXBJY29uIiwiQmFzZU5vZGUiLCJQbGFubmVyTm9kZSIsImRhdGEiLCJjb25maWciLCJwcm92aWRlcklkIiwibW9kZWxJZCIsIm1heFN1YnRhc2tzIiwiZ2V0UHJvdmlkZXJOYW1lIiwiaWQiLCJwcm92aWRlck1hcCIsImdldE1vZGVsRGlzcGxheU5hbWUiLCJwYXJ0cyIsInNwbGl0IiwibGVuZ3RoIiwiaWNvbiIsImNvbG9yIiwiaGFzSW5wdXQiLCJoYXNPdXRwdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwidGVtcGVyYXR1cmUiLCJ1bmRlZmluZWQiLCJwbGFubmluZ1Byb21wdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/index.ts":
/*!****************************************************!*\
  !*** ./src/components/manual-build/nodes/index.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CentralRouterNode: () => (/* reexport safe */ _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   ClassifierNode: () => (/* reexport safe */ _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ConditionalNode: () => (/* reexport safe */ _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   LoopNode: () => (/* reexport safe */ _LoopNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   MemoryNode: () => (/* reexport safe */ _MemoryNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   MergeNode: () => (/* reexport safe */ _MergeNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   OutputNode: () => (/* reexport safe */ _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   ProviderNode: () => (/* reexport safe */ _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   RoleAgentNode: () => (/* reexport safe */ _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   SwitchNode: () => (/* reexport safe */ _SwitchNode__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   ToolNode: () => (/* reexport safe */ _ToolNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   UserRequestNode: () => (/* reexport safe */ _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   VisionNode: () => (/* reexport safe */ _VisionNode__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   nodeTypes: () => (/* binding */ nodeTypes)\n/* harmony export */ });\n/* harmony import */ var _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserRequestNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/UserRequestNode.tsx\");\n/* harmony import */ var _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClassifierNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ClassifierNode.tsx\");\n/* harmony import */ var _ProviderNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProviderNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\");\n/* harmony import */ var _VisionNode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VisionNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\");\n/* harmony import */ var _OutputNode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OutputNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/OutputNode.tsx\");\n/* harmony import */ var _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RoleAgentNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/RoleAgentNode.tsx\");\n/* harmony import */ var _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CentralRouterNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/CentralRouterNode.tsx\");\n/* harmony import */ var _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConditionalNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ConditionalNode.tsx\");\n/* harmony import */ var _MergeNode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MergeNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MergeNode.tsx\");\n/* harmony import */ var _LoopNode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LoopNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/LoopNode.tsx\");\n/* harmony import */ var _ToolNode__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ToolNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\");\n/* harmony import */ var _MemoryNode__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MemoryNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\");\n/* harmony import */ var _SwitchNode__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./SwitchNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/SwitchNode.tsx\");\n/* harmony import */ var _PlannerNode__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PlannerNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx\");\n/* harmony import */ var _BrowsingNode__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./BrowsingNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\");\n// Node Types Registry for React Flow\n// This file exports all custom node components for the Manual Build workflow editor\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Export all node types for React Flow\nconst nodeTypes = {\n    userRequest: _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    classifier: _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    provider: _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    vision: _VisionNode__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    output: _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    roleAgent: _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    centralRouter: _CentralRouterNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    conditional: _ConditionalNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    merge: _MergeNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    loop: _LoopNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    tool: _ToolNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    memory: _MemoryNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    switch: _SwitchNode__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    planner: _PlannerNode__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    browsing: _BrowsingNode__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n// Export individual components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/index.ts\n"));

/***/ })

});