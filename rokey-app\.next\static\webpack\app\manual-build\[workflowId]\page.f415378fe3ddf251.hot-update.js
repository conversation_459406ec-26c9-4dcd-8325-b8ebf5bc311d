"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/ProviderNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction ProviderNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#ff6b35';\n    const providerName = providerId ? providerNames[providerId] : 'AI Provider';\n    // Get connected role nodes - using reactive hooks for automatic re-renders\n    const connectedRoles = edges.filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = nodes.find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            console.log('Role Agent Node Data:', sourceNode.data); // Debug log\n            console.log('Role Config:', roleConfig); // Debug log\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || sourceNode.data.label || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#ff6b35',\n        hasInput: false,\n        hasOutput: true,\n        hasRoleInput: true,\n        hasToolsInput: true,\n        hasBrowsingInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this),\n                            providerId === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"300+ Models\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 15\n                    }, this),\n                    connectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: \"Roles:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: connectedRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                        children: role.name\n                                    }, role.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 68,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"AI Provider Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 121,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(ProviderNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = ProviderNode;\nvar _c;\n$RefreshReg$(_c, \"ProviderNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\n"));

/***/ })

});