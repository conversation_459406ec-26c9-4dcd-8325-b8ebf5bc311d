'use client';

import { Handle, Position } from '@xyflow/react';
import { ReactNode } from 'react';
import { ArrowRightIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { WorkflowNode } from '@/types/manualBuild';

interface InputHandle {
  id: string;
  label: string;
  position: 'left' | 'top';
}

interface BaseNodeProps {
  data: WorkflowNode['data'];
  children?: ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  hasInput?: boolean;
  hasOutput?: boolean;
  hasRoleInput?: boolean;
  hasToolsInput?: boolean;
  hasBrowsingInput?: boolean;
  inputLabel?: string;
  outputLabel?: string;
  roleInputLabel?: string;
  toolsInputLabel?: string;
  browsingInputLabel?: string;
  inputHandles?: InputHandle[]; // Custom input handles for complex nodes
  className?: string;
}

export default function BaseNode({
  data,
  children,
  icon: Icon,
  color = '#ff6b35',
  hasInput = true,
  hasOutput = true,
  hasRoleInput = false,
  hasToolsInput = false,
  hasBrowsingInput = false,
  inputLabel = 'Input',
  outputLabel = 'Output',
  roleInputLabel = 'Role',
  toolsInputLabel = 'Tools',
  browsingInputLabel = 'Browsing',
  inputHandles = [],
  className = ''
}: BaseNodeProps) {
  const isConfigured = data.isConfigured;
  const hasError = data.hasError;

  return (
    <div className={`relative ${className}`}>
      {/* Input Handle with Label */}
      {hasInput && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            id="input"
            className="w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
            style={{ left: -12 }}
          />
          <div className="absolute text-xs text-gray-300 font-medium pointer-events-none" style={{ left: -50, top: '45%' }}>
            {inputLabel}
          </div>
        </>
      )}

      {/* Role Input Handle with Label */}
      {hasRoleInput && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            id="role"
            className="w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors"
            style={{ left: -12, top: '30%' }}
          />
          <div className="absolute text-xs text-purple-200 font-medium pointer-events-none" style={{ left: -50, top: '25%' }}>
            {roleInputLabel}
          </div>
        </>
      )}

      {/* Tools Input Handle with Label */}
      {hasToolsInput && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            id="tools"
            className="w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors"
            style={{ left: -12, top: '70%' }}
          />
          <div className="absolute text-xs text-green-200 font-medium pointer-events-none" style={{ left: -50, top: '65%' }}>
            {toolsInputLabel}
          </div>
        </>
      )}

      {/* Browsing Input Handle with Label */}
      {hasBrowsingInput && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            id="browsing"
            className="w-6 h-6 border-2 border-emerald-500 bg-emerald-700 hover:border-emerald-400 hover:bg-emerald-400 transition-colors"
            style={{ left: -12, top: '85%' }}
          />
          <div className="absolute text-xs text-emerald-200 font-medium pointer-events-none" style={{ left: -60, top: '80%' }}>
            {browsingInputLabel}
          </div>
        </>
      )}

      {/* Custom Input Handles */}
      {inputHandles.length > 0 && inputHandles.map((handle, index) => (
        <div key={handle.id}>
          <Handle
            type="target"
            position={handle.position === 'left' ? Position.Left : Position.Top}
            id={handle.id}
            className="w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors"
            style={{
              left: handle.position === 'left' ? -12 : undefined,
              top: handle.position === 'left' ? `${30 + (index * 35)}%` : -12
            }}
          />
          <div
            className="absolute text-xs text-blue-200 font-medium pointer-events-none"
            style={{
              left: handle.position === 'left' ? -60 : undefined,
              top: handle.position === 'left' ? `${25 + (index * 35)}%` : -25,
              right: handle.position === 'top' ? undefined : 'auto'
            }}
          >
            {handle.label}
          </div>
        </div>
      ))}

      {/* Node Body */}
      <div
        className={`min-w-[200px] rounded-lg border-2 transition-all duration-200 ${
          hasError
            ? 'border-red-500 bg-red-900/20'
            : isConfigured
            ? 'border-gray-600 bg-gray-800/90'
            : 'border-yellow-500 bg-yellow-900/20'
        } backdrop-blur-sm shadow-lg hover:shadow-xl`}
        style={{
          borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'
        }}
      >
        {/* Header */}
        <div 
          className="px-4 py-3 rounded-t-lg flex items-center gap-3"
          style={{
            background: hasError 
              ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))'
              : `linear-gradient(135deg, ${color}20, ${color}10)`
          }}
        >
          {Icon && (
            <div 
              className="p-2 rounded-lg"
              style={{
                backgroundColor: hasError ? '#ef444420' : `${color}20`,
                color: hasError ? '#ef4444' : color
              }}
            >
              <Icon className="w-4 h-4" />
            </div>
          )}
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            {data.description && (
              <div className="text-xs text-gray-400 mt-1">
                {data.description}
              </div>
            )}
          </div>
          
          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            {hasError ? (
              <div className="w-2 h-2 bg-red-500 rounded-full" title="Error" />
            ) : isConfigured ? (
              <div className="w-2 h-2 bg-green-500 rounded-full" title="Configured" />
            ) : (
              <div className="w-2 h-2 bg-yellow-500 rounded-full" title="Needs configuration" />
            )}
          </div>
        </div>



        {/* Content */}
        {children && (
          <div className="px-4 py-3 border-t border-gray-700/50">
            {children}
          </div>
        )}

        {/* Error Message */}
        {hasError && data.errorMessage && (
          <div className="px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg">
            <div className="text-xs text-red-300">
              {data.errorMessage}
            </div>
          </div>
        )}
      </div>

      {/* Output Handle with Label */}
      {hasOutput && (
        <>
          <Handle
            type="source"
            position={Position.Right}
            className="w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors"
            style={{ right: -12 }}
          />
          <div className="absolute text-xs text-orange-200 font-medium pointer-events-none" style={{ right: -60, top: '45%' }}>
            {outputLabel}
          </div>
        </>
      )}
    </div>
  );
}
