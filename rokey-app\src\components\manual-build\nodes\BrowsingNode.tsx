'use client';

import { GlobeAltIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, BrowsingNodeData } from '@/types/manualBuild';

export default function BrowsingNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config as BrowsingNodeData['config'];
  const maxSites = config?.maxSites || 5;
  const timeout = config?.timeout || 30;
  const enableScreenshots = config?.enableScreenshots ?? true;
  const enableFormFilling = config?.enableFormFilling ?? true;
  const searchEngines = config?.searchEngines || ['google'];

  const getCapabilities = () => {
    const capabilities = [];
    if (enableScreenshots) capabilities.push('📸 Screenshots');
    if (enableFormFilling) capabilities.push('📝 Forms');
    if (config?.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');
    return capabilities;
  };

  return (
    <BaseNode
      data={data}
      icon={GlobeAltIcon}
      color="#10b981"
      hasInput={true}
      hasOutput={true}
      inputHandles={[
        { id: 'planner', label: 'Plan', position: 'left' },
        { id: 'memory', label: 'Memory', position: 'left' }
      ]}
    >
      <div className="space-y-3">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-white">
              Intelligent Browsing
            </span>
            <div className="text-xs text-green-400">
              ● Ready
            </div>
          </div>
          
          <div className="text-xs text-gray-400">
            Max sites: {maxSites} | Timeout: {timeout}s
          </div>

          <div className="text-xs text-gray-400">
            Engines: {searchEngines.join(', ')}
          </div>

          {getCapabilities().length > 0 && (
            <div className="text-xs text-gray-400">
              {getCapabilities().join(' • ')}
            </div>
          )}

          {config?.maxDepth && (
            <div className="text-xs text-gray-400">
              Max depth: {config.maxDepth} levels
            </div>
          )}
        </div>

        <div className="text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded">
          🌐 Autonomous Agent
        </div>

        <div className="text-xs text-gray-500">
          Requires: Planner + Memory inputs
        </div>
      </div>
    </BaseNode>
  );
}
