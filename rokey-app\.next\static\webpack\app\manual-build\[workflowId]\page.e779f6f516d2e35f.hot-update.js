"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Web Browsing, Google Drive, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});