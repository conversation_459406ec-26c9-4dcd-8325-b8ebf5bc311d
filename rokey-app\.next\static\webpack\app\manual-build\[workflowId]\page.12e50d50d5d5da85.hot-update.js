"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Role management state\n    const [customRoles, setCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingRoles, setIsLoadingRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rolesError, setRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Fetch custom roles from database\n    const fetchCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchCustomRoles]\": async ()=>{\n            setIsLoadingRoles(true);\n            setRolesError(null);\n            try {\n                const response = await fetch('/api/user/custom-roles');\n                if (!response.ok) {\n                    throw new Error('Failed to fetch custom roles');\n                }\n                const roles = await response.json();\n                setCustomRoles(roles);\n            } catch (err) {\n                console.error('Error fetching custom roles:', err);\n                setRolesError(err.message);\n                setCustomRoles([]);\n            } finally{\n                setIsLoadingRoles(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchCustomRoles]\"], []);\n    // Load models and roles on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision') {\n                fetchModelsFromDatabase();\n            }\n            if (node.type === 'roleAgent') {\n                fetchCustomRoles();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase,\n        fetchCustomRoles\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if ((node.type === 'provider' || node.type === 'vision') && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision')) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // Filter function for vision nodes - only show multimodal models\n                const filterForVision = {\n                    \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (models)=>{\n                        if (node.type === 'vision') {\n                            return models.filter({\n                                \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (model)=>model.modality && (model.modality.includes('multimodal') || model.modality.includes('vision') || model.modality.includes('image'))\n                            }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"]);\n                        }\n                        return models;\n                    }\n                }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"];\n                // If the selected provider is \"OpenRouter\", show all fetched models (filtered for vision if needed)\n                if (currentProviderDetails.id === \"openrouter\") {\n                    const filteredModels = filterForVision(fetchedProviderModels);\n                    return filteredModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    var _deepseekChatModel_modality, _deepseekReasonerModel_modality;\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekChatModel_modality = deepseekChatModel.modality) === null || _deepseekChatModel_modality === void 0 ? void 0 : _deepseekChatModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekReasonerModel_modality = deepseekReasonerModel.modality) === null || _deepseekReasonerModel_modality === void 0 ? void 0 : _deepseekReasonerModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id and vision capabilities\n                const providerModels = fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions].providerModels\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions].providerModels\"]);\n                const filteredModels = filterForVision(providerModels);\n                return filteredModels.map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider' && node.type !== 'vision') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'vision':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                if (nodeConfig.roleType === 'new') {\n                    return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);\n                }\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'centralRouter':\n                return !!nodeConfig.routingStrategy;\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 336,\n            columnNumber: 7\n        }, this);\n    };\n    const renderVisionConfig = ()=>{\n        var _visionConfig_parameters, _visionConfig_parameters1, _visionConfig_parameters2, _visionConfig_parameters3;\n        const visionConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 627,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Vision Model\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-purple-400 ml-1\",\n                                    children: \"(Multimodal Only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...visionConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Vision Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No vision models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this),\n                        modelOptions.length === 0 && (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) && !isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg\",\n                            children: \"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Temperature (0.0 - 2.0)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters = visionConfig.parameters) === null || _visionConfig_parameters === void 0 ? void 0 : _visionConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters1 = visionConfig.parameters) === null || _visionConfig_parameters1 === void 0 ? void 0 : _visionConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 716,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters2 = visionConfig.parameters) === null || _visionConfig_parameters2 === void 0 ? void 0 : _visionConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters3 = visionConfig.parameters) === null || _visionConfig_parameters3 === void 0 ? void 0 : _visionConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate for vision analysis.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 9\n                }, this),\n                (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 font-medium mb-1\",\n                            children: \"\\uD83D\\uDC41️ Vision Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 834,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-purple-200\",\n                            children: \"Access to multimodal models from multiple providers for image analysis and vision tasks.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 835,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 833,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 589,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        const roleConfig = config;\n        // Combine predefined and custom roles for dropdown\n        const availableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>({\n                    id: role.id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'predefined'\n                })),\n            ...customRoles.map((role)=>({\n                    id: role.role_id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'custom'\n                }))\n        ];\n        const handleRoleSelectionChange = (value)=>{\n            if (value === 'create_new') {\n                // Switch to create new role mode\n                const newConfig = {\n                    ...roleConfig,\n                    roleType: 'new',\n                    roleId: '',\n                    roleName: '',\n                    newRoleName: '',\n                    newRoleDescription: '',\n                    customPrompt: ''\n                };\n                setConfig(newConfig);\n                onUpdate({\n                    config: newConfig,\n                    isConfigured: isNodeConfigured(node.type, newConfig)\n                });\n            } else {\n                // Select existing role\n                const selectedRole = availableRoles.find((role)=>role.id === value);\n                if (selectedRole) {\n                    const newConfig = {\n                        ...roleConfig,\n                        roleType: selectedRole.type,\n                        roleId: selectedRole.id,\n                        roleName: selectedRole.name,\n                        customPrompt: selectedRole.description || ''\n                    };\n                    setConfig(newConfig);\n                    onUpdate({\n                        config: newConfig,\n                        isConfigured: isNodeConfigured(node.type, newConfig)\n                    });\n                }\n            }\n        };\n        const handleNewRoleChange = (field, value)=>{\n            const newConfig = {\n                ...roleConfig,\n                [field]: value\n            };\n            setConfig(newConfig);\n            onUpdate({\n                config: newConfig,\n                isConfigured: isNodeConfigured(node.type, newConfig)\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Select Role\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingRoles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400\",\n                            children: \"Loading roles...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' ? 'create_new' : (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) || '',\n                            onChange: (e)=>handleRoleSelectionChange(e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a role...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"System Roles\",\n                                    children: _config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.id,\n                                            children: role.name\n                                        }, role.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, this),\n                                customRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Your Custom Roles\",\n                                    children: customRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.role_id,\n                                            children: role.name\n                                        }, role.role_id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Create New\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"create_new\",\n                                        children: \"+ Create New Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, this),\n                        rolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error loading roles: \",\n                                rolesError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 959,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 915,\n                    columnNumber: 9\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) !== 'new' && (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-white mb-1\",\n                            children: roleConfig.roleName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 13\n                        }, this),\n                        roleConfig.customPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: roleConfig.customPrompt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 967,\n                    columnNumber: 11\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"New Role Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleName || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleName', e.target.value),\n                                    placeholder: \"e.g., Data Analyst, Creative Writer\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 986,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Role Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleDescription || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleDescription', e.target.value),\n                                    placeholder: \"Brief description of this role's purpose\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 995,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Custom Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: roleConfig.customPrompt || '',\n                                    onChange: (e)=>handleNewRoleChange('customPrompt', e.target.value),\n                                    placeholder: \"Enter detailed instructions for this role...\",\n                                    rows: 4,\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.memoryEnabled) || false,\n                                    onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                    className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1026,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-300\",\n                                    children: \"Enable memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1032,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1025,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1 ml-6\",\n                            children: \"Allow this role to remember context from previous interactions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1024,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 913,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1057,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1045,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1064,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1067,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1063,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1081,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1077,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1076,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1044,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1108,\n            columnNumber: 7\n        }, this);\n    };\n    const renderCentralRouterConfig = ()=>{\n        const routerConfig = config;\n        var _routerConfig_enableCaching, _routerConfig_debugMode;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Routing Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.routingStrategy) || 'smart',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    routingStrategy: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"smart\",\n                                    children: \"Smart Routing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"round_robin\",\n                                    children: \"Round Robin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"load_balanced\",\n                                    children: \"Load Balanced\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"priority\",\n                                    children: \"Priority Based\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"How the router selects between available AI providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Retries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"0\",\n                            max: \"10\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.maxRetries) || 3,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    maxRetries: parseInt(e.target.value) || 3\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Number of retry attempts on failure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (ms)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1000\",\n                            max: \"300000\",\n                            step: \"1000\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.timeout) || 30000,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    timeout: parseInt(e.target.value) || 30000\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Request timeout in milliseconds\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Enable Caching\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_enableCaching = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.enableCaching) !== null && _routerConfig_enableCaching !== void 0 ? _routerConfig_enableCaching : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            enableCaching: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Cache responses to improve performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Debug Mode\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_debugMode = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.debugMode) !== null && _routerConfig_debugMode !== void 0 ? _routerConfig_debugMode : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            debugMode: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Enable detailed logging for debugging\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1252,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1140,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolConfig = ()=>{\n        var _toolOptions_find, _toolOptions_find1;\n        const toolConfig = config;\n        const toolOptions = [\n            {\n                value: '',\n                label: 'Select a tool...'\n            },\n            {\n                value: 'web_browsing',\n                label: '🌐 Web Browsing (Free)',\n                description: 'Browse websites and extract information'\n            },\n            {\n                value: 'google_drive',\n                label: '📁 Google Drive',\n                description: 'Access and manage Google Drive files'\n            },\n            {\n                value: 'google_docs',\n                label: '📄 Google Docs',\n                description: 'Create and edit Google Documents'\n            },\n            {\n                value: 'google_sheets',\n                label: '📊 Google Sheets',\n                description: 'Work with Google Spreadsheets'\n            },\n            {\n                value: 'zapier',\n                label: '⚡ Zapier',\n                description: 'Connect with 5000+ apps via Zapier'\n            },\n            {\n                value: 'notion',\n                label: '📝 Notion',\n                description: 'Access Notion databases and pages'\n            },\n            {\n                value: 'calendar',\n                label: '📅 Calendar',\n                description: 'Manage calendar events and schedules'\n            },\n            {\n                value: 'gmail',\n                label: '📧 Gmail',\n                description: 'Send and manage emails'\n            },\n            {\n                value: 'youtube',\n                label: '📺 YouTube',\n                description: 'Access YouTube data and analytics'\n            },\n            {\n                value: 'supabase',\n                label: '🗄️ Supabase',\n                description: 'Direct database operations'\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Tool Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    toolType: e.target.value,\n                                    // Reset tool-specific config when changing tool type\n                                    toolConfig: {},\n                                    // Web browsing is always ready, others need authentication\n                                    connectionStatus: e.target.value === 'web_browsing' ? 'connected' : 'disconnected',\n                                    isAuthenticated: e.target.value === 'web_browsing',\n                                    // Set default search engine for web browsing\n                                    searchEngine: e.target.value === 'web_browsing' ? 'google' : undefined,\n                                    extractionType: e.target.value === 'web_browsing' ? 'content' : undefined\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: toolOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: option.value,\n                                    children: option.label\n                                }, option.value, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1329,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1305,\n                            columnNumber: 11\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: (_toolOptions_find = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find === void 0 ? void 0 : _toolOptions_find.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1301,\n                    columnNumber: 9\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) === 'web_browsing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-green-400\",\n                                    children: \"Ready to use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Search Engine\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.searchEngine) || 'google',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...toolConfig,\n                                            searchEngine: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"google\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1368,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"bing\",\n                                            children: \"Bing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1349,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Extraction Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.extractionType) || 'content',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...toolConfig,\n                                            extractionType: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"content\",\n                                            children: \"Extract Content\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"search\",\n                                            children: \"Search Results\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"screenshot\",\n                                            children: \"Take Screenshot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"custom\",\n                                            children: \"Custom Selector\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1377,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1373,\n                            columnNumber: 13\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.extractionType) === 'custom' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"CSS Selector\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1401,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.customSelector) || '',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...toolConfig,\n                                            customSelector: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    placeholder: \"e.g., .content, #main, h1\",\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1404,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"CSS selector to extract specific content from the page\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1400,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1343,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && toolConfig.toolType !== 'web_browsing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-yellow-400\",\n                                    children: \"Authentication Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1432,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mb-2\",\n                                    children: [\n                                        (_toolOptions_find1 = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find1 === void 0 ? void 0 : _toolOptions_find1.label,\n                                        \" integration coming soon!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1438,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"This tool will require account linking and authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1441,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1437,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1431,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"5\",\n                            max: \"300\",\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1454,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum time to wait for the tool operation to complete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1472,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1450,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1300,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'vision':\n                return renderVisionConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'centralRouter':\n                return renderCentralRouterConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            case 'tool':\n                return renderToolConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1506,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1505,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1509,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1512,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1504,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1517,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1503,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1536,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1532,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1540,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1531,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 1501,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"3qPqzANwA5DE2PGqVNbp9Fhx4wo=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});