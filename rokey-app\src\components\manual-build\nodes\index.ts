// Node Types Registry for React Flow
// This file exports all custom node components for the Manual Build workflow editor

import UserRequestNode from './UserRequestNode';
import ClassifierNode from './ClassifierNode';
import ProviderNode from './ProviderNode';
import VisionNode from './VisionNode';
import OutputNode from './OutputNode';
import RoleAgentNode from './RoleAgentNode';
import CentralRouterNode from './CentralRouterNode';
import ConditionalNode from './ConditionalNode';
import MergeNode from './MergeNode';
import LoopNode from './LoopNode';
import ToolNode from './ToolNode';
import MemoryNode from './MemoryNode';
import SwitchNode from './SwitchNode';
import PlannerNode from './PlannerNode';
import BrowsingNode from './BrowsingNode';

// Export all node types for React Flow
export const nodeTypes = {
  userRequest: UserRequestNode,
  classifier: ClassifierNode,
  provider: ProviderNode,
  vision: VisionNode,
  output: OutputNode,
  roleAgent: RoleAgentNode,
  centralRouter: CentralRouterNode,
  conditional: ConditionalNode,
  merge: MergeNode,
  loop: LoopNode,
  tool: ToolNode,
  memory: MemoryNode,
  switch: SwitchNode,
  planner: PlannerNode,
  browsing: BrowsingNode,
};

// Export individual components
export {
  UserRequestNode,
  ClassifierNode,
  ProviderNode,
  VisionNode,
  OutputNode,
  RoleAgentNode,
  CentralRouterNode,
  ConditionalNode,
  MergeNode,
  LoopNode,
  ToolNode,
  MemoryNode,
  SwitchNode,
  PlannerNode,
  BrowsingNode,
};
